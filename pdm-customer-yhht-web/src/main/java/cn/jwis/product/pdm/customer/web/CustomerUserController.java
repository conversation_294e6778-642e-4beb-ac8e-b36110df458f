package cn.jwis.product.pdm.customer.web;


import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.response.Result;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.platform.iam.gateway.access.annotation.IgnoreRestUrlAccess;
import cn.jwis.platform.plm.account.auth.service.AuthHelper;
import cn.jwis.platform.plm.account.user.service.UserHelper;
import cn.jwis.product.pdm.customer.service.dto.PdmUserAuthInfo;
import cn.jwis.product.pdm.customer.service.dto.PdmUserInfo;
import cn.jwis.product.pdm.customer.service.dto.PdmUserQueryParams;
import cn.jwis.product.pdm.customer.service.interf.CommonService;
import cn.jwis.product.pdm.customer.service.interf.CustomerUserHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 人员信息
 */
@RestController
@RequestMapping(value = "/user")
@Api(tags = "客制化人员查询", value = "客制化人员查询", description = "客制化人员查询")
public class CustomerUserController {

    private static final Logger log = LoggerFactory.getLogger(CustomerUserController.class);
    @Autowired
    private CustomerUserHelper customerUserHelper;

    @Resource
    private UserHelper userHelper;
    @Autowired
    private CommonService commonService;
    @Autowired
    private AuthHelper authHelper;

    @GetMapping(value = "/list")
     public Result<?> list(){
         return Result.success(customerUserHelper.getAllUserInfo());
     }

    @ApiOperation("获取用户认证信息")
    @PostMapping("/getUserAuthInfo")
    @IgnoreRestUrlAccess
    public Result<PdmUserAuthInfo> getUserAuthInfo(
            @ApiParam("查询参数") @RequestBody PdmUserQueryParams params) {
        log.info("当前入参params:{}", params);
        String account = params.getAccount();
        UserDTO byAccount = this.userHelper.findByAccount(account);
        if (byAccount == null) {
            throw new JWIException("该用户账号" + account + "不存在");
        }
        String tenantOid = commonService.getAloneTenantOid();
        byAccount.setTenantOid(tenantOid);
        byAccount.setOid(byAccount.getOid());
        byAccount.setAccount(account);
        this.authHelper.fillUserAuthInfo(byAccount);
        String accessToken = SessionHelper.getAccessToken();
        SessionHelper.setAccessToken(accessToken);
        SessionHelper.addCurrentUser(byAccount);
        SessionHelper.setAppId("pdm");
        PdmUserAuthInfo authInfo = new PdmUserAuthInfo();
        authInfo.setAccessToken(accessToken);
        PdmUserInfo pdmUserInfo = new PdmUserInfo();
        pdmUserInfo.setAccount(byAccount.getAccount());
        pdmUserInfo.setOid(byAccount.getOid());
        pdmUserInfo.setTenantOid(tenantOid);
        authInfo.setUserInfo(pdmUserInfo);
        return Result.success(authInfo);
    }

}
