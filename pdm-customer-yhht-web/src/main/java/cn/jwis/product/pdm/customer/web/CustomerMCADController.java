package cn.jwis.product.pdm.customer.web;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ZipUtil;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.response.Result;
import cn.jwis.framework.base.util.Assert;
import cn.jwis.framework.base.util.OidGenerator;
import cn.jwis.platform.iam.gateway.access.annotation.IgnoreRestUrlAccess;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.file.entity.FileMetadata;
import cn.jwis.platform.plm.file.service.FileService;
import cn.jwis.platform.plm.foundation.attachment.entity.File;
import cn.jwis.platform.plm.foundation.common.dto.RelatedFuzzyDTO;
import cn.jwis.platform.plm.foundation.common.response.InstanceEntity;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelper;
import cn.jwis.platform.plm.foundation.common.service.InstanceHelper;
import cn.jwis.platform.plm.foundation.relationship.enums.RelationConstraint;
import cn.jwis.platform.plm.sysconfig.collectionrule.service.CollectionRuleHelper;
import cn.jwis.platform.plm.sysconfig.entity.CollectionRule;
import cn.jwis.product.pdm.cad.mcad.dto.MCADCreateDTO;
import cn.jwis.product.pdm.cad.mcad.entity.CADFile;
import cn.jwis.product.pdm.cad.mcad.entity.MCADIteration;
import cn.jwis.product.pdm.cad.mcad.relation.Primary;
import cn.jwis.product.pdm.cad.mcad.service.MCADHelper;
import cn.jwis.product.pdm.cad.mcad.service.MCADService;
import cn.jwis.product.pdm.customer.service.impl.CustomerFIleServiceImpl;
import cn.jwis.product.pdm.customer.service.util.CustomerMinioUtil;
import cn.jwis.product.pdm.partbom.part.entity.Part;
import cn.jwis.product.pdm.partbom.part.entity.PartIteration;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ych
 * @Date ： 2024/10/23
 * @Description :mcad创建Controller
 */

@Slf4j
@RestController
@RequestMapping({"/mcad"})
@Api(tags = {"mcad"},value = "mcad", description = "mcad")
public class CustomerMCADController {

    @Autowired
    CommonAbilityHelper commonAbilityHelper;

    @Autowired
    MCADService mcadService;
    @Autowired
    private InstanceHelper instanceHelper;

    @Autowired
    FileService fileService;

    @Autowired
    private JWICommonService jwiCommonService;

    @Autowired
    private CollectionRuleHelper collectionRuleHelper;

    @Autowired
    private MCADHelper mcadHelper;

    @Autowired
    private CustomerFIleServiceImpl customerFileService;

    @Autowired
    private CustomerMinioUtil customerMinioUtil;

    // 临时文件路径配置
    private static final boolean isWindows = System.getProperty("os.name").toLowerCase().contains("windows");
    private static final String fileSeparator = java.io.File.separator;
    private static final String tmpPath = isWindows ? ("C:" + fileSeparator + "tmp" + fileSeparator) : (fileSeparator + "tmp" + fileSeparator);
    @Value("${minio.default.bucket}")
    private String bucketName;

    @PostMapping({"/create"})
    @ApiOperation(
            response = Result.class,
            value = "customer创建MCAD-重写",
            notes = "customer创建MCAD-重写"
    )
    public Result create(@RequestBody MCADCreateDTO dto) throws JWIException {
        MCADIteration mcadIteration = new MCADIteration();
        BeanUtils.copyProperties(dto, mcadIteration);
        MCADIteration byName = this.mcadService.findByName(dto.getName());
        if (null != byName) {
            return Result.Fail("已存在相同名称MCAD对象 编码:" + byName.getNumber() + " 名称:" + byName.getName());
        }
        this.mcadService.setLocation(mcadIteration, dto.getLocationInfo());
        MCADIteration result = commonAbilityHelper.doCreate(mcadIteration);
        result.setLifecycleStatus("Released");
        result = commonAbilityHelper.doUpdate(result);

        List<File> primaryFile = dto.getPrimaryFile();
        if (null != primaryFile && primaryFile.size() > 0) {
            File file = primaryFile.get(0);
            FileMetadata fileMetadata = fileService.findByOid(file.getOid());
            Assert.notNull(fileMetadata, "文件不存在");
            CADFile cadFile = new CADFile();
            cadFile.setOid(OidGenerator.newOid());
            cadFile.setType(CADFile.TYPE);
            cadFile.setFileType("Creo 7.0");
            cadFile.setPrimary(Boolean.TRUE);
            cadFile.setLastModified(DateUtil.now());
            cadFile.setFileName(fileMetadata.getFileOriginalName());
            cadFile.setUrl(fileMetadata.getOid());
            CADFile cadFile1 = mcadService.createCADFile(cadFile);
            mcadService.linkMCADFile(mcadIteration.getOid(), cadFile1.getOid(), Primary.TYPE);
        }
        return Result.success(result);
    }

    /**
     * 根据物料号查询MCAD对象文件
     *
     * @param partNumbers 物料号列表
     * @return 包含压缩文件下载链接和统计信息的结果
     */
    @IgnoreRestUrlAccess
    @PostMapping("/queryMCADFilesByPartNumbers")
    @ApiOperation(
            response = Result.class,
            value = "根据物料号查询MCAD对象文件",
            notes = "根据一组物料号，查询物料号关联的MCAD对象的STP格式文件和CADDrawing的附件，打包成zip并返回下载链接"
    )
    public Result<MCADFilesZipResultDTO> queryMCADFilesByPartNumbers(
            @ApiParam(value = "物料号列表", required = true)
            @RequestBody List<String> partNumbers) {

        try {
            log.info("开始查询物料号对应的MCAD文件，物料号列表: {}", partNumbers);

            // 存储所有需要下载的文件信息
            Map<String, List<FileDownloadInfo>> partFilesMap = new HashMap<>();
            List<String> noFilesPartNumbers = new ArrayList<>();

            // 为每个物料号查询文件
            for (String partNumber : partNumbers) {
                log.info("正在处理物料号: {}", partNumber);

                // 查询相关的MCAD对象
                List<InstanceEntity> relatedMCADs = queryRelatedMCADsByPartNumber(partNumber);
                List<FileDownloadInfo> partFiles = new ArrayList<>();

                for (InstanceEntity mcadInstance : relatedMCADs) {
                    // 收集MCAD文件
                    List<FileDownloadInfo> mcadFiles = collectMCADFilesForDownload(mcadInstance, partNumber);
                    partFiles.addAll(mcadFiles);
                }

                if (partFiles.isEmpty()) {
                    noFilesPartNumbers.add(partNumber);
                    log.warn("物料号 {} 未找到任何MCAD文件", partNumber);
                } else {
                    partFilesMap.put(partNumber, partFiles);
                    log.info("物料号 {} 找到 {} 个文件", partNumber, partFiles.size());
                }
            }

            // 下载文件并打包
            String zipDownloadUrl = downloadFilesAndCreateZip(partFilesMap);

            // 构建返回结果
            MCADFilesZipResultDTO result = new MCADFilesZipResultDTO();
            result.setZipDownloadUrl(zipDownloadUrl);
            result.setTotalPartNumbers(partNumbers.size());
            result.setFoundPartNumbers(partFilesMap.size());
            result.setNoFilesPartNumbers(noFilesPartNumbers);
            result.setNoFilesCount(noFilesPartNumbers.size());

            log.info("查询完成，总物料号: {}, 找到文件的物料号: {}, 未找到文件的物料号: {}",
                    result.getTotalPartNumbers(), result.getFoundPartNumbers(), result.getNoFilesCount());

            return Result.success(result);

        } catch (Exception e) {
            log.error("查询MCAD文件失败", e);
            return Result.Fail("查询MCAD文件失败: " + e.getMessage());
        }
    }

    /**
     * 根据料号查询相关的MCAD对象
     */
    private List<InstanceEntity> queryRelatedMCADsByPartNumber(String partNumber) {
        // 首先根据料号查找Part对象
        ArrayList<String> slaveNumbers = new ArrayList<>();
        slaveNumbers.add(partNumber);
        List<PartIteration> parts = commonAbilityHelper.findDetailEntityByNumber(slaveNumbers, PartIteration.TYPE)
                .stream().map(item -> (PartIteration) item).collect(Collectors.toList());

        List<InstanceEntity> allRelatedMCADs = new ArrayList<>();
        // 查询Part关联的MCAD图档
        for (PartIteration part : parts) {
            List<InstanceEntity> relatedMCADs = queryWithCollectionRule(
                    part.getOid(),
                    "Part_Related_Object",
                    "相关MCAD图档",
                    Part.TYPE,
                    RelationConstraint.II
            );
            //查询MCAD关联的图纸
            for (InstanceEntity relatedMCAD : relatedMCADs) {
                List<InstanceEntity> relatedCADDrawings = queryWithCollectionRule(
                        relatedMCAD.getOid(),
                        "MCAD_Related_Object",
                        "工程图",
                        relatedMCAD.getModelDefinition(),
                        RelationConstraint.MI
                );
                allRelatedMCADs.addAll(relatedCADDrawings);
            }
            allRelatedMCADs.addAll(relatedMCADs);
        }


        return allRelatedMCADs;
    }

    /**
     * 查询集合规则相关对象
     */
    private List<InstanceEntity> queryWithCollectionRule(String oid,String appliedType,String ruleName,
                                                         String mainObjectType,RelationConstraint relationConstraint) {
        CollectionRule collectionRule = collectionRuleHelper.findByAppliedType(appliedType, mainObjectType)
                .stream().filter(it -> ruleName.equals(it.getRelationDisplayName())).findFirst().orElse(null);

        return Optional.ofNullable(collectionRule).map(rule -> {
            RelatedFuzzyDTO relatedFuzzyDTO = new RelatedFuzzyDTO();
            relatedFuzzyDTO.setRelationConstraint(relationConstraint);
            BeanUtils.copyProperties(collectionRule, relatedFuzzyDTO);
            relatedFuzzyDTO.setMainObjectOid(oid);
            return Optional.ofNullable(instanceHelper.fuzzyRelated(relatedFuzzyDTO)).orElse(Collections.emptyList());
        }).orElse(Collections.emptyList());
    }

    /**
     * 收集MCAD文件用于下载
     */
    private List<FileDownloadInfo> collectMCADFilesForDownload(InstanceEntity mcadInstance, String partNumber) {
        List<FileDownloadInfo> result = new ArrayList<>();

        try {
            String modelDefinition = mcadInstance.getModelDefinition();
            List<CADFile> cadFiles = new ArrayList<>();

            switch (modelDefinition) {
                case "CADPart":
                case "CADAssembly":
                    // 4.2.1 零件和装配：优先查找STP格式文件，如果没有则查找源文件
                    cadFiles = mcadHelper.findCADFileSTPByOid(mcadInstance.getOid(), "stp");
                    if (cadFiles.isEmpty()) {
                        cadFiles = mcadHelper.findCADFileSTPByOid(mcadInstance.getOid(), "primary");
                    }
                    break;

                case "CADDrawing":
                    // 4.2.2 工程图：查找附件（已签名的PDF）
                    cadFiles = mcadHelper.findCADFile(mcadInstance.getOid())
                            .stream()
                            .filter(cadFile -> !cadFile.isPrimary() && "thumbnail".equals(cadFile.getFileType()))
                            .collect(ArrayList::new, (list, item) -> list.add(item), ArrayList::addAll);
                    break;

                default:
                    break;
            }

            // 处理每个CAD文件
            for (CADFile cadFile : cadFiles) {
                try {
                    // 获取文件元数据
                    FileMetadata fileMetadata = fileService.findByOid(cadFile.getUrl());
                    if (fileMetadata == null) {
                        log.warn("文件元数据不存在: {}", cadFile.getUrl());
                        continue;
                    }

                    // 生成带前缀的文件名：PartNumber_原文件名
                    String originalFileName = cadFile.getFileName();
                    if (originalFileName == null || originalFileName.trim().isEmpty()) {
                        originalFileName = fileMetadata.getFileOriginalName();
                    }
                    String prefixedFileName = partNumber + "_" + originalFileName;

                    // 生成下载链接
                    String downloadUrl = customerFileService.getUrlByOidForPreview(
                            cadFile.getUrl(),
                            mcadInstance.getType(),
                            mcadInstance.getOid()
                    );

                    // 解码Base64下载链接
                    String actualDownloadUrl = new String(Base64.getDecoder().decode(downloadUrl), StandardCharsets.UTF_8);

                    FileDownloadInfo fileInfo = new FileDownloadInfo();
                    fileInfo.setPartNumber(partNumber);
                    fileInfo.setOriginalFileName(originalFileName);
                    fileInfo.setPrefixedFileName(prefixedFileName);
                    fileInfo.setDownloadUrl(actualDownloadUrl);
                    fileInfo.setFileOid(cadFile.getUrl());
                    fileInfo.setMcadOid(mcadInstance.getOid());
                    fileInfo.setMcadName(mcadInstance.getName());
                    fileInfo.setMcadType(modelDefinition);

                    result.add(fileInfo);
                    log.debug("添加文件: {} -> {}", originalFileName, prefixedFileName);

                } catch (Exception e) {
                    log.error("处理CAD文件失败: {}, 错误: {}", cadFile.getFileName(), e.getMessage(), e);
                }
            }

        } catch (Exception e) {
            log.error("收集MCAD文件失败: {}, 错误: {}", mcadInstance.getOid(), e.getMessage(), e);
        }

        return result;
    }

    /**
     * 下载文件并创建ZIP包
     */
    private String downloadFilesAndCreateZip(Map<String, List<FileDownloadInfo>> partFilesMap) throws Exception {
        if (partFilesMap.isEmpty()) {
            throw new JWIException("没有文件需要下载");
        }

        // 创建临时目录
        String timestamp = DateUtil.format(new Date(), "yyyyMMddHHmmss");
        String tempDir = tmpPath + "mcad_files_" + timestamp + fileSeparator;
        FileUtil.mkdir(tempDir);

        try {
            // 下载所有文件到临时目录
            for (Map.Entry<String, List<FileDownloadInfo>> entry : partFilesMap.entrySet()) {
                String partNumber = entry.getKey();
                List<FileDownloadInfo> files = entry.getValue();

                // 为每个物料号创建子目录
                String partDir = tempDir + partNumber + fileSeparator;
                FileUtil.mkdir(partDir);

                for (FileDownloadInfo fileInfo : files) {
                    try {
                        String localFilePath = partDir + fileInfo.getPrefixedFileName();
                        java.io.File localFile = new java.io.File(localFilePath);

                        log.info("正在下载文件: {} -> {}", fileInfo.getDownloadUrl(), localFilePath);
                        cn.hutool.http.HttpUtil.downloadFile(fileInfo.getDownloadUrl(), localFile);
                        log.info("文件下载成功: {}", localFilePath);

                    } catch (Exception e) {
                        log.error("下载文件失败: {}, 错误: {}", fileInfo.getPrefixedFileName(), e.getMessage(), e);
                        // 继续下载其他文件，不中断整个流程
                    }
                }
            }

            // 创建ZIP文件
            String zipFileName = "MCAD_Files_" + timestamp + ".zip";
            String zipFilePath = tmpPath + zipFileName;
            java.io.File zipFile = new java.io.File(zipFilePath);

            log.info("正在创建ZIP文件: {}", zipFilePath);
            ZipUtil.zip(tempDir, zipFilePath);
            log.info("ZIP文件创建成功: {}", zipFilePath);

            // 上传ZIP文件到MinIO
            String minioObjectName = "mcad-files/" + zipFileName;
            String uploadUrl = customerMinioUtil.getPresignedUploadUrl(bucketName, minioObjectName, 30);

            log.info("正在上传ZIP文件到MinIO: {}", minioObjectName);
            // 使用PUT方法上传文件
            cn.hutool.http.HttpRequest.put(uploadUrl)
                    .body(FileUtil.readBytes(zipFile))
                    .execute();

            // 生成下载链接
            String downloadUrl = customerMinioUtil.getPresignedDownloadUrl(bucketName, minioObjectName, 60 * 24 * 7); // 7*24小时有效
            log.info("ZIP文件上传成功，下载链接: {}", downloadUrl);

            return downloadUrl;

        } finally {
            // 清理临时文件
            try {
                FileUtil.del(tempDir);
                log.info("临时目录清理完成: {}", tempDir);
            } catch (Exception e) {
                log.warn("清理临时目录失败: {}, 错误: {}", tempDir, e.getMessage());
            }
        }
    }

    // 内部类定义
    public static class FileDownloadInfo {
        private String partNumber;
        private String originalFileName;
        private String prefixedFileName;
        private String downloadUrl;
        private String fileOid;
        private String mcadOid;
        private String mcadName;
        private String mcadType;

        // getters and setters
        public String getPartNumber() { return partNumber; }
        public void setPartNumber(String partNumber) { this.partNumber = partNumber; }
        public String getOriginalFileName() { return originalFileName; }
        public void setOriginalFileName(String originalFileName) { this.originalFileName = originalFileName; }
        public String getPrefixedFileName() { return prefixedFileName; }
        public void setPrefixedFileName(String prefixedFileName) { this.prefixedFileName = prefixedFileName; }
        public String getDownloadUrl() { return downloadUrl; }
        public void setDownloadUrl(String downloadUrl) { this.downloadUrl = downloadUrl; }
        public String getFileOid() { return fileOid; }
        public void setFileOid(String fileOid) { this.fileOid = fileOid; }
        public String getMcadOid() { return mcadOid; }
        public void setMcadOid(String mcadOid) { this.mcadOid = mcadOid; }
        public String getMcadName() { return mcadName; }
        public void setMcadName(String mcadName) { this.mcadName = mcadName; }
        public String getMcadType() { return mcadType; }
        public void setMcadType(String mcadType) { this.mcadType = mcadType; }
    }

    public static class MCADFilesZipResultDTO {
        private String zipDownloadUrl;
        private int totalPartNumbers;
        private int foundPartNumbers;
        private int noFilesCount;
        private List<String> noFilesPartNumbers;

        // getters and setters
        public String getZipDownloadUrl() { return zipDownloadUrl; }
        public void setZipDownloadUrl(String zipDownloadUrl) { this.zipDownloadUrl = zipDownloadUrl; }
        public int getTotalPartNumbers() { return totalPartNumbers; }
        public void setTotalPartNumbers(int totalPartNumbers) { this.totalPartNumbers = totalPartNumbers; }
        public int getFoundPartNumbers() { return foundPartNumbers; }
        public void setFoundPartNumbers(int foundPartNumbers) { this.foundPartNumbers = foundPartNumbers; }
        public int getNoFilesCount() { return noFilesCount; }
        public void setNoFilesCount(int noFilesCount) { this.noFilesCount = noFilesCount; }
        public List<String> getNoFilesPartNumbers() { return noFilesPartNumbers; }
        public void setNoFilesPartNumbers(List<String> noFilesPartNumbers) { this.noFilesPartNumbers = noFilesPartNumbers; }
    }
}
