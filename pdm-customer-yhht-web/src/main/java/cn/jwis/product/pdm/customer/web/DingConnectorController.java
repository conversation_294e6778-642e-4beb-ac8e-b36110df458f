package cn.jwis.product.pdm.customer.web;


import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.response.Result;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.platform.iam.gateway.access.annotation.IgnoreRestUrlAccess;
import cn.jwis.platform.plm.account.user.service.UserHelper;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.workflow.engine.order.entity.ProcessOrder;
import cn.jwis.platform.plm.workflow.engine.service.interf.ProcessOrderHelper;
import cn.jwis.product.pdm.customer.entity.DingTaskRecord;
import cn.jwis.product.pdm.customer.entity.IntegrationRecord;
import cn.jwis.product.pdm.customer.service.dto.DingConnectorRequest;
import cn.jwis.product.pdm.customer.service.impl.CustomerCommonServiceImpl;
import cn.jwis.product.pdm.customer.service.interf.DingTalkService;
import cn.jwis.product.pdm.customer.service.release.SMISRelease;
import com.alibaba.fastjson.JSON;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiProcessinstanceGetRequest;
import com.dingtalk.api.response.OapiProcessinstanceGetResponse;
import com.taobao.api.ApiException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedHashSet;
import java.util.List;

@RestController
@RequestMapping(value = "/dingConnector/")
@Api(tags = "钉钉连接器接口", value = "钉钉连接器接口", description = "钉钉连接器接口")
@Slf4j
public class DingConnectorController {

    public static String ADMIN_DD_USER_ID_YCH = "17159354798539272";
    public static String ADMIN_DD_USER_ID_LJT = "1663118953935349";
    public static String CORP_ID = "ding53c2a71231b4fe2935c2f4657eb6378f";

    @Autowired
    JWICommonService jwiCommonService;
    @Autowired
    DingTalkService dingTalkService;

    @Autowired
    private SMISRelease smisRelease;

    @Resource
    ProcessOrderHelper processOrderHelper;

    @Resource
    private UserHelper userHelper;

    @Autowired
    CustomerCommonServiceImpl customerCommonService;

    /**
     * 钉钉连接器接口 - PCN推送SMIS
     * 参考findFolderByContainerOid的实现
     *
     * @param request 钉钉连接器请求参数
     * @return 推送结果
     */
    @RequestMapping(value = "/pcnPushToSmis", method = RequestMethod.POST)
    @ApiOperation(response = Result.class, value = "钉钉连接器-PCN推送SMIS")
    @IgnoreRestUrlAccess
    public Result pcnPushToSmis(@RequestBody DingConnectorRequest request) {
        String dingProcessInstanceId = request.getDingProcessInstanceId();
        log.info("钉钉连接器PCN推送SMIS开始，request: {}", JSONUtil.toJsonStr(request));

        // 参数验证
        if (StrUtil.isEmpty(dingProcessInstanceId)) {
            log.error("钉钉流程实例ID不能为空");
            return Result.Fail("钉钉流程实例ID不能为空");
        }

        try {
            // 1. 根据钉钉流程ID查询对应的DingTaskRecord
            DingTaskRecord dingTaskRecord = jwiCommonService.dynamicQueryOne(
                    DingTaskRecord.TYPE,
                    Condition.where("dingProcessInstanceId").eq(dingProcessInstanceId),
                    DingTaskRecord.class
            );

            if (dingTaskRecord == null) {
                log.error("未找到对应的钉钉任务记录，dingProcessInstanceId: {}", dingProcessInstanceId);
                return Result.Fail("未找到对应的钉钉任务记录: " + dingProcessInstanceId);
            }

            log.info("找到DingTaskRecord: {}", JSONUtil.toJsonStr(dingTaskRecord));

            //设置 businessId，并更新数据
            dingTaskRecord.setBusinessId(dingProcessInstanceId);
            jwiCommonService.update(dingTaskRecord);
            // 2. 执行cancelPdmBomWorkFlow逻辑
            cancelPdmBomWorkFlow(dingTaskRecord);
            log.info("PDM BOM工作流取消完成");

            // 3. 执行SMIS推送逻辑
            IntegrationRecord integrationRecord = smisRelease.release(null, dingTaskRecord.getOid(), null, 0);

            if (integrationRecord != null && integrationRecord.getIsSuccess()) {
                log.info("PCN推送SMIS成功，businessOid: {}", integrationRecord.getBusinessOid());
                return Result.success("PCN推送SMIS成功");
            } else {
                String errorMsg = integrationRecord != null ? integrationRecord.getMsg() : "推送失败，未返回记录";
                log.error("PCN推送SMIS失败: {}", errorMsg);

                // 4. 推送失败时发送钉钉通知
                sendPcnFailureNotification(dingTaskRecord, dingProcessInstanceId, errorMsg);

                return Result.Fail("PCN推送SMIS失败: " + errorMsg);
            }

        } catch (Exception e) {
            log.error("钉钉连接器PCN推送SMIS异常: {}", e.getMessage(), e);

            try {
                // 查询DingTaskRecord用于发送通知
                DingTaskRecord dingTaskRecord = jwiCommonService.dynamicQueryOne(
                        DingTaskRecord.TYPE,
                        Condition.where("dingProcessInstanceId").eq(dingProcessInstanceId),
                        DingTaskRecord.class
                );

                if (dingTaskRecord != null) {
                    sendPcnFailureNotification(dingTaskRecord, dingProcessInstanceId, e.getMessage());
                }
            } catch (Exception notifyException) {
                log.error("发送失败通知时异常: {}", notifyException.getMessage(), notifyException);
            }

            return Result.Fail("PCN推送SMIS异常: " + e.getMessage());
        }
    }

    /**
     * 取消PDM BOM工作流
     * 从DingTalkCallListen中移植的逻辑
     */
    private void cancelPdmBomWorkFlow(DingTaskRecord dingTaskRecord) {
        String owner = dingTaskRecord.getOwner();
        UserDTO byAccount = userHelper.findByAccount(owner);
        byAccount.setTenantOid(dingTaskRecord.getTenantOid());
        SessionHelper.addCurrentUser(byAccount);
        try {
            //取消pdm的流程
            ProcessOrder processOrder1 = processOrderHelper.cancelProcess(dingTaskRecord.getPdmProcessOrderId());
            log.info("PDM流程状态.processOrder1---->>>{}", JSON.toJSONString(processOrder1));
        } catch (Exception e) {
            log.error("processOrder1取消出错", e);
        }
    }

    /**
     * 发送PCN推送失败通知
     *
     * @param dingTaskRecord 钉钉任务记录
     * @param dingProcessInstanceId 钉钉流程实例ID
     * @param errorMsg 错误信息
     */
    private void sendPcnFailureNotification(DingTaskRecord dingTaskRecord, String dingProcessInstanceId, String errorMsg) {
        try {
            // 构造钉钉流程链接
            String dingProcessUrl = buildDingProcessUrl(dingProcessInstanceId);
            // 构造通知消息
            String notificationMessage = String.format(
                    "PCN推送SMIS失败通知\n" +
                            "失败原因: %s\n" +
                            "钉钉流程链接: %s\n" +
                            "钉钉流程ID: %s\n" +
                            "请及时处理",
                    errorMsg,
                    dingProcessUrl,
                    dingProcessInstanceId
            );
            String tokenNew = dingTalkService.getTokenNew();
            //获取钉钉发起人的userId

            String processOriginatorUserId = getProcessOriginatorUserId(tokenNew, dingProcessInstanceId);
            log.info("当前流程发起者：{}", processOriginatorUserId);
            List<String> userIdList = new ArrayList<>(new LinkedHashSet<>(Arrays.asList(
                    ADMIN_DD_USER_ID_LJT,
                    ADMIN_DD_USER_ID_YCH,
                    processOriginatorUserId
            )));

            dingTalkService.sendRobotDING(tokenNew, userIdList, notificationMessage);

            log.info("PCN推送失败通知已发送");

        } catch (Exception e) {
            log.error("发送PCN推送失败通知异常: {}", e.getMessage(), e);
        }
    }

    private String buildDingProcessUrl(String procInstId) throws UnsupportedEncodingException {
        //构造 deeplink url
        String approvalUrl = "https://aflow.dingtalk.com/dingtalk/mobile/homepage.htm?corpid="
                + URLEncoder.encode(CORP_ID, StandardCharsets.UTF_8.toString())
                + "&dd_share=false&showmenu=false&dd_progress=false&back=native&procInstId="
                + URLEncoder.encode(procInstId, StandardCharsets.UTF_8.toString())
                + "&taskId=&swfrom=isv&dinghash=approval&dtaction=os&dd_from=corp#approval";
        String encodedUrl = URLEncoder.encode(approvalUrl, StandardCharsets.UTF_8.toString());
        return "dingtalk://dingtalkclient/page/link?url=" + encodedUrl + "&pc_slide=true";

    }

    /**
     * 根据流程实例ID获取发起人UserId
     *
     * @param accessToken           钉钉token
     * @param dingProcessInstanceId 流程实例ID
     * @return 发起人userId
     */
    public static String getProcessOriginatorUserId(String accessToken, String dingProcessInstanceId) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/processinstance/get");
        OapiProcessinstanceGetRequest req = new OapiProcessinstanceGetRequest();
        req.setProcessInstanceId(dingProcessInstanceId);
        try {
            OapiProcessinstanceGetResponse rsp = client.execute(req, accessToken);
            if (rsp != null && rsp.isSuccess()) {
                return rsp.getProcessInstance().getOriginatorUserid();
            } else {
                throw new RuntimeException("获取流程发起人失败: " + (rsp == null ? "响应为空" : rsp.getErrmsg()));
            }
        } catch (ApiException e) {
            throw new RuntimeException("调用钉钉接口异常: " + e.getMessage(), e);
        }
    }
}
