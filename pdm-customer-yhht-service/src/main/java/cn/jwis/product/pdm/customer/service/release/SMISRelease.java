package cn.jwis.product.pdm.customer.service.release;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.workflow.engine.order.entity.ProcessOrder;
import cn.jwis.product.pdm.change.entity.ECR;
import cn.jwis.product.pdm.customer.entity.DingTaskRecord;
import cn.jwis.product.pdm.customer.entity.IntegrationRecord;
import cn.jwis.product.pdm.customer.repo.CustomerCommonRepo;
import cn.jwis.product.pdm.customer.service.impl.DingTalkServiceImpl;
import cn.jwis.product.pdm.customer.service.impl.SMISCall;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceResponseBody;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static sun.font.FontUtilities.isWindows;

@Data
@Component
@Slf4j
public class SMISRelease extends EntityRelease{

    public final static String fileSeparator = java.io.File.separator;
    public final static String tmpPath = isWindows ? ("C:" + fileSeparator + "tmp" + fileSeparator) : (fileSeparator + "tmp" + fileSeparator);

    @Autowired
    JWICommonService jwiCommonService;
    @Autowired
    DingTalkServiceImpl dingTalkServiceImpl;
    @Autowired
    SMISCall smisCall;
    @Autowired
    private CustomerCommonRepo customerCommonRepo;


    @Override
    public IntegrationRecord release(ProcessOrder processOrder, String entityOid, String recordOid, int isFirst) {
        try {
            // 根据entityOid查找DingTaskRecord
            DingTaskRecord dingTaskRecord = findDingTaskRecordByOid(entityOid);
            if (dingTaskRecord == null) {
                throw new JWIException("未找到对应的钉钉任务记录: " + entityOid);
            }

            // 执行PCN变更发布逻辑
            return executePCNRelease(dingTaskRecord, processOrder, recordOid);

        } catch (Exception e) {
            log.error("SMIS PCN发布失败: entityOid={}, error={}", entityOid, e.getMessage(), e);

            // 创建失败记录
            JSONObject failureData = new JSONObject();
            failureData.put("businessOid", "pcn_" + entityOid);
            failureData.put("entityOid", entityOid);
            failureData.put("error", e.getMessage());

            IntegrationRecord record = getIntegrationRecord(recordOid, type, processOrder, failureData);
            record.setConsumer("SMIS");
            record.setIsSuccess(false);
            record.setMsg(e.getMessage());
            record.setOperationType("PCN_PUSH");

            return jwiCommonService.update(record);
        }
    }

    /**
     * 执行PCN变更发布
     */
    private IntegrationRecord executePCNRelease(DingTaskRecord dingTaskRecord, ProcessOrder processOrder, String recordOid) throws Exception {
        String dingProcessInstanceId = dingTaskRecord.getDingProcessInstanceId();
        String businessId = dingTaskRecord.getBusinessId();

        // 先获取ECR信息用于构造基础业务数据
        String ecrOid = "";
        ECR ecr = null;
        try {
            List<String> ecrOidList = dingTaskRecord.getEcrOidList();
            if(ecrOidList != null && ecrOidList.size() > 0){
                ecrOid = ecrOidList.get(0);
                ecr = jwiCommonService.dynamicQueryOne(ECR.TYPE, Condition.where("oid").eq(ecrOid), ECR.class);
            }
        } catch (Exception e) {
            log.warn("获取ECR信息失败: {}", e.getMessage());
        }

        // 构造基础业务数据用于记录
        JSONObject businessData = new JSONObject();
        businessData.put("businessOid", businessId);
        businessData.put("dingProcessInstanceId", dingProcessInstanceId);
        businessData.put("number", businessId);

        if (ecr != null) {
            businessData.put("name", ecr.getName() + "_" + ecr.getNumber());
            businessData.put("oid", ecr.getOid());
            businessData.put("type", ecr.getType());
            businessData.put("modelDefinition", ecr.getModelDefinition());
        } else {
            businessData.put("name", "PCN变更_" + businessId);
            businessData.put("oid", dingTaskRecord.getOid());
            businessData.put("type", DingTaskRecord.TYPE);
            businessData.put("modelDefinition", DingTaskRecord.TYPE);
        }

        // 立即创建并保存集成记录，确保无论后续是否出错都有记录
        log.info("当前businessData:{}", JSONUtil.toJsonStr(businessData));
        IntegrationRecord record = createAndSaveRecord(recordOid, processOrder, businessData);
        log.info("已创建PCN发布记录: {}", JSONUtil.toJsonStr(record));

        Map<String, Object> formData = new HashMap<>();
        try {
            // 获取钉钉token和表单数据
            String tokenNew = dingTalkServiceImpl.getTokenNew();
            List<GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultFormComponentValues> dingFromValues =
                getDingFromValues(dingProcessInstanceId, tokenNew);

            // 处理物料清单数据
            formData = processFormData(dingFromValues, dingProcessInstanceId, businessId);

            // 更新业务数据
            businessData.put("formDataKeys", new ArrayList<>(formData.keySet()));
            businessData.put("itemCount", getItemCount(formData));
//            record.setData(businessData);
            record.setMsg("数据处理完成，准备调用SMIS接口...");
            record = updateRecordInNewTransaction(record);
            log.info("PCN发布记录-更新1获取钉钉表单数据: {}", JSONUtil.toJsonStr(record));
        } catch (Exception e) {
            log.error("处理钉钉表单数据失败: {}", e.getMessage(), e);
            record.setIsSuccess(false);
            record.setMsg("处理钉钉表单数据失败: " + e.getMessage());
            return updateRecordInNewTransaction(record);
        }

        try {
            // 调用SMIS接口
            String result = smisCall.postFormData(smisCall.getPcnPushUrl(), formData);
            log.info("SMIS PCN推送结果: {}", result);

            // 解析结果并更新记录
            updateRecordWithResult(record, result,dingTaskRecord);

        } catch (Exception e) {
            log.error("调用SMIS接口失败: {}", e.getMessage(), e);
            record.setIsSuccess(false);
            record.setMsg("调用SMIS接口失败: " + e.getMessage());
        } finally {
            // 清理临时文件
            cleanupTempFiles(formData);
        }
        log.info("PCN发布记录-更新2调用SMIS接口: {}", JSONUtil.toJsonStr(record));
        return updateRecordInNewTransaction(record);
    }

    /**
     * 在新事务中创建并保存IntegrationRecord
     * 使用REQUIRES_NEW确保即使外层事务回滚，这个记录也会被保存
     */
    private IntegrationRecord createAndSaveRecord(String recordOid, ProcessOrder processOrder, JSONObject businessData) {
        IntegrationRecord record = getIntegrationRecord(recordOid, type, processOrder, businessData);
        record.setConsumer("SMIS");
        record.setOperationType("PCN_PUSH");
        record.setIsSuccess(false); // 默认设置为失败，成功时再更新
        record.setMsg("正在处理PCN变更发布...");

        // 在新事务中保存记录
        record = jwiCommonService.update(record);
        return record;
    }

    /**
     * 在新事务中更新IntegrationRecord
     * 使用REQUIRES_NEW确保即使外层事务回滚，这个更新也会被保存
     */
    private IntegrationRecord updateRecordInNewTransaction(IntegrationRecord record) {
        return jwiCommonService.update(record);
    }

    /**
     * 查找钉钉任务记录
     */
    private DingTaskRecord findDingTaskRecordByOid(String entityOid) {
        // 这里可能需要根据实际的业务逻辑来查找DingTaskRecord
        // 如果entityOid就是DingTaskRecord的oid，直接查找
        try {
            return jwiCommonService.dynamicQueryOne(DingTaskRecord.TYPE,
                Condition.where("oid").contain(entityOid), DingTaskRecord.class);
        } catch (Exception e) {
            log.warn("通过oid查找DingTaskRecord失败，尝试其他方式: {}", e.getMessage());
            // 如果直接查找失败，可能需要通过其他字段查找
            return jwiCommonService.dynamicQueryOne(DingTaskRecord.TYPE,
                Condition.where("businessId").eq(entityOid), DingTaskRecord.class);
        }
    }

    /**
     * 获取钉钉表单数据
     */
    private List<GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultFormComponentValues> getDingFromValues(String processInstanceId, String token) throws Exception {
        return dingTalkServiceImpl.getDingFromValues(processInstanceId, token);
    }

    /**
     * 处理钉钉表单数据，构造SMIS接口所需的form-data
     */
    private Map<String, Object> processFormData(List<GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultFormComponentValues> formComponentValues,
                                               String processInstanceId, String businessId) throws Exception {
        Map<String, Object> formData = new HashMap<>();
        List<Map<String, Object>> items = new ArrayList<>();
        List<File> allFiles = new ArrayList<>();
        List<String> changeContentAttachmentFiles = new ArrayList<>();
        String changeContentDesc = "";

        // 第一遍遍历：处理更改内容说明和附件
        for (GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultFormComponentValues formComponentValue : formComponentValues) {
            String name = formComponentValue.getName();
            if (StrUtil.isNotEmpty(name) && "更改内容说明".equals(name)) {
                changeContentDesc = formComponentValue.getValue();
                log.info("更改内容说明: {}", changeContentDesc);
            }
            if (StrUtil.isNotEmpty(name) && "更改内容说明附件".equals(name)) {
                // 处理更改内容说明附件
                String attachmentJson = formComponentValue.getValue();
                log.info("更改内容说明附件Json: {}", attachmentJson);
                changeContentAttachmentFiles = processChangeContentAttachments(attachmentJson, processInstanceId, allFiles);
                log.info("更改内容说明附件处理完成，文件数量: {}", changeContentAttachmentFiles.size());
            }
        }

        // 第二遍遍历：处理物料清单
        for (GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultFormComponentValues formComponentValue : formComponentValues) {
            String name = formComponentValue.getName();
            if (StrUtil.isNotEmpty(name) && "物料清单".equals(name)) {
                String wlmxJson = formComponentValue.getValue();
                log.info("物料清单Json: {}", wlmxJson);

                JSONArray jsonArray = JSONUtil.parseArray(wlmxJson);
                for (int i = 0; i < jsonArray.size(); i++) {
                    cn.hutool.json.JSONObject obj = jsonArray.getJSONObject(i);
                    cn.hutool.json.JSONArray rowValueArray = obj.getJSONArray("rowValue");

                    // 解析每行数据，并将更改内容说明附件添加到每个物料中
                    Map<String, Object> item = processRowData(rowValueArray, processInstanceId, allFiles, changeContentAttachmentFiles);
                    if (item != null) {
                        items.add(item);
                    }
                }
                break; // 找到物料清单后跳出循环
            }
        }

        // 构造data对象
        Map<String, Object> data = new HashMap<>();
        data.put("orderno", businessId);
        data.put("items", items);
        data.put("changeContentDesc", changeContentDesc);

        // 将data转换为JSON字符串并添加到formData
        formData.put("data", JSONUtil.toJsonStr(data));
        //这里是钉钉表单中的 变更内容说明

        // 添加所有文件到formData
        for (int i = 0; i < allFiles.size(); i++) {
            File file = allFiles.get(i);
            formData.put("file" + (i + 1), file);
        }

        log.info("构造的formData keys: {}, 文件数量: {}", formData.keySet(), allFiles.size());
        return formData;
    }

    /**
     * 处理更改内容说明附件
     */
    private List<String> processChangeContentAttachments(String attachmentJson, String processInstanceId, List<File> allFiles) throws Exception {
        List<String> fileNames = new ArrayList<>();

        if (StrUtil.isEmpty(attachmentJson) || "[]".equals(attachmentJson)) {
            log.info("更改内容说明附件为空");
            return fileNames;
        }

        try {
            JSONArray fileJsonArray = JSONUtil.parseArray(attachmentJson);
            if (fileJsonArray != null && fileJsonArray.size() > 0) {
                log.info("开始处理更改内容说明附件，数量: {}", fileJsonArray.size());

                for (int i = 0; i < fileJsonArray.size(); i++) {
                    cn.hutool.json.JSONObject fileObj = fileJsonArray.getJSONObject(i);
                    String fileId = fileObj.getStr("fileId");
                    String fileName = fileObj.getStr("fileName");

                    if (StrUtil.isNotEmpty(fileId) && StrUtil.isNotEmpty(fileName)) {
                        // 下载文件
                        File downloadedFile = downloadAttachment(processInstanceId, fileId, fileName);
                        if (downloadedFile != null) {
                            allFiles.add(downloadedFile);
                            // 使用文件索引作为key名称
                            String fileKey = "file" + (allFiles.size());
                            fileNames.add(fileKey);
                            log.info("成功处理更改内容说明附件: {} -> {}", fileName, fileKey);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理更改内容说明附件失败: {}", e.getMessage(), e);
            throw e;
        }

        return fileNames;
    }

    /**
     * 处理表格中的每行数据
     */
    private Map<String, Object> processRowData(cn.hutool.json.JSONArray rowValueArray, String processInstanceId, List<File> allFiles, List<String> changeContentAttachmentFiles) throws Exception {
        Map<String, Object> item = new HashMap<>();

        // 提取各个字段值
        String itemCode = getStringByKeyName(rowValueArray, "物料编码", "");
        String name = getStringByKeyName(rowValueArray, "名称", "");
        String specification = getStringByKeyName(rowValueArray, "规格", "");
        String workDefinition = getStringByKeyName(rowValueArray, "工艺路线版本", "");
        String affectedWorkRequests = getStringByKeyName(rowValueArray, "受影响工单", "");
        String affectedWorks = getStringByKeyName(rowValueArray, "受影响工序", "");
        String changeDetail = getStringByKeyName(rowValueArray, "工艺说明", "");
        String processFiles = getStringByKeyName(rowValueArray, "工艺规程", "[]");

        // 验证必要字段
        if (StrUtil.isEmpty(itemCode)) {
            log.warn("物料编码为空，跳过该行数据");
            return null;
        }

        // 处理工艺路线版本（逗号分隔）
        List<String> workDefinitions = splitAndTrim(workDefinition,Boolean.FALSE);

        // 处理受影响工单（逗号分隔）
        List<String> workRequestsList = splitAndTrim(affectedWorkRequests, Boolean.FALSE);

        // 处理受影响工序（逗号分隔）
        List<String> worksList = splitAndTrim(affectedWorks, Boolean.TRUE);

        // 处理工艺规程附件
        List<String> fileNames = processAttachments(processFiles, processInstanceId, allFiles);

        // 将更改内容说明附件添加到每个物料的文件列表中
        List<String> allFileNames = new ArrayList<>();
        allFileNames.addAll(fileNames); // 先添加工艺规程附件
        allFileNames.addAll(changeContentAttachmentFiles); // 再添加更改内容说明附件

        // 构造item对象
        item.put("itemcode", itemCode);
        item.put("workdefinition", workDefinitions.isEmpty() ? workDefinition : workDefinitions.get(0)); // 取第一个工艺路线版本
        item.put("workrequests", workRequestsList);
        item.put("works", worksList);
        item.put("changedetail", changeDetail);
        item.put("files", allFileNames);

        log.info("处理行数据完成 - 物料编码: {}, 工艺规程附件: {}, 更改内容说明附件: {}, 总文件数量: {}",
                itemCode, fileNames.size(), changeContentAttachmentFiles.size(), allFileNames.size());
        return item;
    }

    /**
     * 分割字符串并去除空格
     */
    private List<String> splitAndTrim(String value, Boolean isWorksList) {
        List<String> result = new ArrayList<>();
        if (StrUtil.isNotEmpty(value)) {
            String[] parts = value.split(",");
            for (String part : parts) {
                String trimmed = StrUtil.trim(part);
                if (StrUtil.isNotEmpty(trimmed)) {
                    result.add(isWorksList
                            ? StrUtil.subBefore(trimmed, "-", false).trim()
                            : trimmed);
                }
            }
        }
        return result;
    }

    /**
     * 处理附件文件
     */
    private List<String> processAttachments(String processFilesJson, String processInstanceId, List<File> allFiles) throws Exception {
        List<String> fileNames = new ArrayList<>();

        if (StrUtil.isEmpty(processFilesJson) || "[]".equals(processFilesJson)) {
            return fileNames;
        }

        try {
            JSONArray fileJsonArray = JSONUtil.parseArray(processFilesJson);
            if (fileJsonArray != null && fileJsonArray.size() > 0) {
                for (int i = 0; i < fileJsonArray.size(); i++) {
                    cn.hutool.json.JSONObject fileObj = fileJsonArray.getJSONObject(i);
                    String fileId = fileObj.getStr("fileId");
                    String fileName = fileObj.getStr("fileName");

                    if (StrUtil.isNotEmpty(fileId) && StrUtil.isNotEmpty(fileName)) {
                        // 下载文件
                        File downloadedFile = downloadAttachment(processInstanceId, fileId, fileName);
                        if (downloadedFile != null) {
                            allFiles.add(downloadedFile);
                            // 使用文件索引作为key名称
                            String fileKey = "file" + (allFiles.size());
                            fileNames.add(fileKey);
                            log.info("成功处理附件: {} -> {}", fileName, fileKey);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理附件失败: {}", e.getMessage(), e);
            throw e;
        }

        return fileNames;
    }

    /**
     * 下载钉钉附件到本地临时文件
     */
    private File downloadAttachment(String processInstanceId, String fileId, String fileName) throws Exception {
        try {
            // 获取下载链接
            String downloadUri = dingTalkServiceImpl.getDINGFileDownloadUrl(processInstanceId, fileId);
            if (StrUtil.isEmpty(downloadUri)) {
                log.error("获取文件下载链接失败: fileId={}", fileId);
                return null;
            }

            // 构造本地文件路径
            String docFileTmpSavePath = tmpPath + fileName;
            File localFile = FileUtil.file(docFileTmpSavePath);

            // 下载文件
            HttpUtil.downloadFile(downloadUri, localFile);
            log.info("文件下载成功: {} -> {}", fileName, docFileTmpSavePath);

            return localFile;
        } catch (Exception e) {
            log.error("下载附件失败: fileName={}, fileId={}, error={}", fileName, fileId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 从表格行数据中根据字段名获取值
     */
    private static String getStringByKeyName(cn.hutool.json.JSONArray rowValueArray, String keyName, String defaultValue) {
        for (int k = 0; k < rowValueArray.size(); k++) {
            cn.hutool.json.JSONObject innerRowObj = rowValueArray.getJSONObject(k);
            if (keyName.equals(innerRowObj.getStr("label"))) {
                String value = innerRowObj.getStr("value");
                return StrUtil.isNotEmpty(value) ? value : defaultValue;
            }
        }
        return defaultValue;
    }

    /**
     * 更新记录结果
     */
    private void updateRecordWithResult(IntegrationRecord record, String result, DingTaskRecord dingTaskRecord) {
        try {
            // 解析SMIS返回结果
            if (StrUtil.isNotEmpty(result)) {
                // 这里根据SMIS的实际返回格式来解析
                JSONObject resultJson = JSONObject.parseObject(result);
                String message = resultJson.getString("msg");
                String code = resultJson.getString("code");
                boolean success = "0".equals(code);

                record.setIsSuccess(success);
                record.setMsg(success ? "PCN推送成功" : ("PCN推送失败: " + message));
            } else {
                record.setIsSuccess(false);
                record.setMsg("SMIS接口返回空结果");
            }
            if (record.getIsSuccess()) {
                setECROidsStatus(dingTaskRecord);
            }
        } catch (Exception e) {
            log.warn("解析SMIS返回结果失败，默认为成功: {}", e.getMessage());
            record.setIsSuccess(true);
            record.setMsg("PCN推送完成，返回结果: " + result);
        }
    }

    /**
     * 获取items数量
     */
    private int getItemCount(Map<String, Object> formData) {
        try {
            String dataStr = (String) formData.get("data");
            if (StrUtil.isNotEmpty(dataStr)) {
                JSONObject dataJson = JSONObject.parseObject(dataStr);
                com.alibaba.fastjson.JSONArray items = dataJson.getJSONArray("items");
                return items != null ? items.size() : 0;
            }
        } catch (Exception e) {
            log.warn("获取items数量失败: {}", e.getMessage());
        }
        return 0;
    }

    /**
     * 清理临时文件
     */
    private void cleanupTempFiles(Map<String, Object> formData) {
        try {
            for (Map.Entry<String, Object> entry : formData.entrySet()) {
                if (entry.getKey().startsWith("file") && entry.getValue() instanceof File) {
                    File file = (File) entry.getValue();
                    if (file.exists()) {
                        boolean deleted = file.delete();
                        log.info("清理临时文件: {} - {}", file.getPath(), deleted ? "成功" : "失败");
                    }
                }
            }
        } catch (Exception e) {
            log.warn("清理临时文件失败: {}", e.getMessage());
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        type = "pcn"; // 设置业务类型为pcn
        EntityReleaseFactory.register(type, this);
        log.info("SMISRelease注册成功，业务类型: {}", type);
    }

    /**
     * PCN流程结束设置ECR对象为 待实施
     * @param dingTaskRecord
     */
    private void setECROidsStatus(DingTaskRecord dingTaskRecord) {
        String lifecycleStatus = "Pending";

        // 记录操作开始的日志
        log.info("开始更新ECR对象生命周期状态。流程：PCN结束，目标状态：{}，涉及的ECR OID列表：{}",
                lifecycleStatus, dingTaskRecord.getEcrOidList());

        customerCommonRepo.updateLifeStatus(ECR.TYPE, dingTaskRecord.getEcrOidList(), lifecycleStatus);

        // 记录操作完成的日志
        log.info("ECR对象生命周期状态更新完成。ECR OID列表：{}，已更新至状态：{}",
                dingTaskRecord.getEcrOidList(), "待实施");
    }
}
