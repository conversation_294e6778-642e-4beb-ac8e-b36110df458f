package cn.jwis.product.pdm.customer.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 钉钉连接器请求DTO
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@ApiModel("钉钉连接器请求")
public class DingConnectorRequest {
    
    @ApiModelProperty(value = "钉钉流程实例ID", required = true, example = "proc_inst_12345")
    private String dingProcessInstanceId;
    
    @ApiModelProperty(value = "操作类型", required = false, example = "PCN_PUSH")
    private String operationType;
    
    @ApiModelProperty(value = "备注信息", required = false)
    private String remark;
}
