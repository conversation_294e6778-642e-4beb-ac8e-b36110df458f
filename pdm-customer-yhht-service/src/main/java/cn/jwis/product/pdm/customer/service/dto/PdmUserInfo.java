package cn.jwis.product.pdm.customer.service.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * PDM用户信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PdmUserInfo {
    
    @ApiModelProperty("用户OID")
    private String oid;
    
    @ApiModelProperty("用户账号")
    private String account;
    
    @ApiModelProperty("用户名称")
    private String name;
    
    @ApiModelProperty("邮箱")
    private String email;
    
    @ApiModelProperty("租户OID")
    private String tenantOid;
    
    @ApiModelProperty("租户名称")
    private String tenantName;
    
    @ApiModelProperty("部门OID")
    private String departmentOid;
    
    @ApiModelProperty("部门名称")
    private String departmentName;
    
    @ApiModelProperty("角色信息")
    private String roles;
    
    @ApiModelProperty("用户状态")
    private String status;
    
    @ApiModelProperty("创建时间")
    private String createTime;
    
    @ApiModelProperty("最后登录时间")
    private String lastLoginTime;
}
