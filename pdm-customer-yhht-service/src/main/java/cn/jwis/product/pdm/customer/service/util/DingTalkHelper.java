package cn.jwis.product.pdm.customer.service.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.resource.InputStreamResource;
import cn.hutool.core.io.resource.MultiResource;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.domain.able.info.ModelInfo;
import cn.jwis.framework.base.domain.entity.BaseEntity;
import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.util.Assert;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.base.util.OidGenerator;
import cn.jwis.framework.base.util.ValidationUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.framework.database.core.entity.SubFilter;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.framework.database.neo4j.session.Neo4jSession;
import cn.jwis.platform.plm.account.auth.service.AuthHelper;
import cn.jwis.platform.plm.account.tenant.service.TenantService;
import cn.jwis.platform.plm.account.user.service.UserHelper;
import cn.jwis.platform.plm.container.entity.Container;
import cn.jwis.platform.plm.container.entity.Folder;
import cn.jwis.platform.plm.container.entity.FolderTreeNode;
import cn.jwis.platform.plm.container.service.FolderHelper;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.file.entity.FileMetadata;
import cn.jwis.platform.plm.foundation.attachment.entity.File;
import cn.jwis.platform.plm.foundation.classification.able.info.ClassificationInfo;
import cn.jwis.platform.plm.foundation.classification.entity.Classification;
import cn.jwis.platform.plm.foundation.classification.service.ClassificationHelper;
import cn.jwis.platform.plm.foundation.common.param.RelatedBatchOperate;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelper;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityService;
import cn.jwis.platform.plm.foundation.common.service.InstanceHelper;
import cn.jwis.platform.plm.foundation.location.able.info.LocationInfo;
import cn.jwis.platform.plm.foundation.relationship.Contain;
import cn.jwis.platform.plm.foundation.relationship.Reference;
import cn.jwis.platform.plm.foundation.relationship.enums.RelationConstraint;
import cn.jwis.platform.plm.foundation.versionrule.able.info.LockInfo;
import cn.jwis.platform.plm.workflow.engine.service.interf.ProcessModelHelper;
import cn.jwis.platform.plm.workflow.engine.service.interf.ProcessTeamHelper;
import cn.jwis.platform.plm.workflow.engine.workflow.service.interf.ProcessOrderService;
import cn.jwis.product.pdm.customer.entity.DingTaskRecord;
import cn.jwis.product.pdm.customer.entity.FileDownloadInfo;
import cn.jwis.product.pdm.customer.entity.assistant.CheckedAssist;
import cn.jwis.product.pdm.customer.entity.assistant.SafeWrapAssist;
import cn.jwis.product.pdm.customer.repo.CustomerCommonRepo;
import cn.jwis.product.pdm.customer.service.impl.CustomerDeliveryHelperImpl;
import cn.jwis.product.pdm.customer.service.impl.CustomerProcessOrderHelper;
import cn.jwis.product.pdm.customer.service.impl.DingTalkServiceImpl;
import cn.jwis.product.pdm.customer.service.interf.CommonService;
import cn.jwis.product.pdm.customer.service.interf.DingTalkService;
import cn.jwis.product.pdm.customer.service.interf.IntegrationMonitorService;
import cn.jwis.product.pdm.customer.service.interf.PDMFolderServiceI;
import cn.jwis.product.pdm.delivery.dto.DeliveryCreateDTO;
import cn.jwis.product.pdm.delivery.entity.Delivery;
import cn.jwis.product.pdm.delivery.helper.DeliveryHelper;
import cn.jwis.product.pdm.document.entity.Document;
import cn.jwis.product.pdm.document.entity.DocumentIteration;
import cn.jwis.product.pdm.document.service.DocumentHelper;
import cn.jwis.product.pdm.document.service.DocumentService;
import cn.jwis.product.pdm.partbom.part.entity.PartIteration;
import cn.jwis.product.pdm.partbom.part.service.PartHelper;
import com.alibaba.fastjson.JSON;
import com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenRequest;
import com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenResponse;
import com.aliyun.dingtalkstorage_1_0.models.GetFileDownloadInfoResponse;
import com.aliyun.dingtalkstorage_1_0.models.GetFileDownloadInfoResponseBody;
import com.aliyun.dingtalkworkflow_1_0.Client;
import com.aliyun.dingtalkworkflow_1_0.models.*;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.Common;
import com.aliyun.teautil.models.RuntimeOptions;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiV2UserGetRequest;
import com.dingtalk.api.response.OapiV2UserGetResponse;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.annotation.Resource;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.jwis.product.pdm.document.service.DocumentHelper.REDISSON_DOCUMENT_NAME_KEY;
import static sun.font.FontUtilities.isWindows;

@Service
@Slf4j
@Getter
@Setter
public class DingTalkHelper implements InitializingBean, CheckedAssist, SafeWrapAssist {


    @Value("${dingTalk.appKeyNew}")
    private String dingTalkAppKey;

    @Value("${dingTalk.appSecretNew}")
    private String dingTalkAppSecret;

    @Value("${dingTalk.agentIdNew}")
    private long dingTalkAgentId;

    @Value("${dingTalk.switch.on:false}")
    private boolean dingTalkSwitch;

    @Value("${dingTalk.name.taskName:cn_jwis_bzh}")
    private String dingTalkNameTaskName;

    @Autowired
    RedisTemplate redisTemplate;

    @Autowired
    DingTalkHelper dingTalkCall;
    @Autowired
    ProcessModelHelper processModelHelper;
    @Autowired
    ProcessTeamHelper processTeamHelper;
    @Autowired
    ProcessOrderService processOrderService;
    @Autowired
    Neo4jSession neo4jSession;

    @Autowired
    PDMFolderServiceI pDMFolderServiceI;
    @Autowired
    private DingTalkService dingTalkService;

    @Autowired
    private IntegrationMonitorService integrationMonitorService;

    @Resource
    private UserHelper userHelper;

    @Autowired
    JWICommonService jwiCommonService;

    @Autowired
    TenantService tenantService;

    @Resource
    private PDMFolderServiceI pdmFolderServiceI;

    @Autowired
    private ClassificationHelper classificationHelper;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private DocumentService documentService;

    @Autowired
    private CommonAbilityHelper commonAbilityHelper;

    @Autowired
    private DeliveryHelper deliveryHelper;

    @Autowired
    private CommonAbilityService commonAbilityService;

    @Autowired
    private DocumentHelper docAppService;

    @Resource
    JWICommonService commonService;

    @Resource
    private FolderHelper folderHelper;

    @Value("${file.service.gateway.url}")
    private String fileServiceGatewayUrl;

    @Autowired
    CustomerProcessOrderHelper processOrderHelper;

    @Autowired
    private CustomerCommonRepo customerCommonRepo;

    @Autowired
    private CommonService commonService1;

    @Autowired
    private AuthHelper authHelper;

    @Resource
    CustomerDeliveryHelperImpl customerDeliveryHelper;
    @Autowired
    FIleOutMailUtil fIleOutMailUtil;

    @Autowired
    private PartHelper partHelper;

    @Autowired
    private InstanceHelper instanceHelper;

    public final static String fileSeparator = java.io.File.separator, tmpPath = isWindows ? ("C:" + fileSeparator + "tmp" + fileSeparator) : (fileSeparator + "tmp" + fileSeparator);

    /**
     * 通过 ProcessInstanceId 生成文档，上传文件
     *
     * @param bizData 流程实例ID
     */
    @Transactional
    public void createOrChangeDocForSoftWareConfig(com.alibaba.fastjson.JSONObject bizData, boolean isChange) {
        log.info("createOrChangeDocForSoftWareConfig START------>");
        String processInstanceId = bizData.getString("processInstanceId");
        String processBusinessId = bizData.getString("businessId");
        String storageTime = bizData.getString("finishTime");
        String subTime = bizData.getString("createTime");
        try {
            //1、获取钉钉审批流程数据
            String accessToken = getTokenNew();
            GetProcessInstanceResponseBody getProcessInstanceResponseBody = processInstancesInfo(processInstanceId, accessToken);
            GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResult result = getProcessInstanceResponseBody.getResult();
            List<GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultFormComponentValues> formComponentValues = result.getFormComponentValues();
            List<GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultOperationRecords> operationRecords = result.getOperationRecords();

            //pdm这里需要的信息只有 1、产品容器id 2、要上传的附件 3、创建人

            String createBy = null;
            String containerOid = "";
            String folderOid = "";
            String docOid = "";
            FolderTreeNode folderNode = null;
            String softwareList = null;
            String deliveryOid = null;
            String downloadUri;
            String fileName;
            List<String> documentList = new ArrayList<>();

            //提取发起流程人信息
            for (GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultOperationRecords record : operationRecords) {
                if ("提交申请".equals(record.getShowName())) {
                    String userId = record.getUserId();
                    log.info("Calling getUserDetail with userId: {}, accessToken: {}", userId, accessToken);
                    OapiV2UserGetResponse.UserGetResponse rsp = dingTalkService.getUserDetail(userId, accessToken);
                    log.info("Response from getUserDetail: {}", JSONUtil.toJsonStr(rsp));
                    String email = rsp.getEmail();
                    Assert.notNull(email, "软件配置项首次建库配置流程 发起人 email 为空");
                    if (email != null && email.contains("@")) {
                        createBy = email.split("@")[0];
                    }
                    break;
                }
            }

            //初始化账号信息
            initContext(createBy);
            String token = getTokenNew();
            //提取产品相关信息
            for (GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultFormComponentValues componentValue : formComponentValues) {
                switch (componentValue.getName()) {
                    case "PDM型号名称":
                        String extValueContainer = componentValue.getExtValue();
                        containerOid= extractContainerOid(extValueContainer);
                        log.info("extValue: {}", JSONUtil.toJsonStr(extValueContainer));
                        Assert.notEmpty(containerOid, "产品容器oid为空");
                        break;
                    case "PDM目录":
                        String extValueFolder = componentValue.getExtValue();
                        folderOid = extractContainerOid(extValueFolder);
                        log.info("extValueFolder: {}", JSONUtil.toJsonStr(extValueFolder));
                        Assert.notEmpty(folderOid, "产品容器文件夹oid为空");
                    case "PDM变更文件":
                        String extValueDoc = componentValue.getExtValue();
                        docOid = extractContainerOid(extValueDoc);
                        log.info("extValueDoc: {}", JSONUtil.toJsonStr(extValueDoc));
                        Assert.notEmpty(docOid, "产品容器文件夹下文件oid为空");
                        break;
                    case "软件配置项清单（含有流程、权限、负责人）" :
                    case "本次变更后的软件配置项文档（PDM归档材料）":
                        softwareList = componentValue.getValue();
                        break;
                    default:
                        break;
                }
            }

            JSONArray jsonArray = JSONUtil.parseArray(softwareList);
            // 获取附件下载路径
            if (jsonArray != null && !jsonArray.isEmpty()) {
                log.info("软件配置项清单jsonArray{}", JSONUtil.toJsonStr(jsonArray));
                // 获取第一个元素并转换为JSONObject
                JSONObject firstElement = jsonArray.getJSONObject(0);
                String fileId = firstElement.getStr("fileId");
                fileName = firstElement.getStr("fileName");
                downloadUri = getDINGFileDownloadUrl(processInstanceId, fileId, token);
            } else {
                throw new JWIException("钉钉流程 上传附件为空 ");
            }

            //2、根据流程数据来处理pdm内业务

            DocumentIteration doc = new DocumentIteration();

            if (isChange) {
                doc = docAppService.findByOid(docOid);
            }else {
                if (null == deliveryOid || StrUtil.isEmpty(deliveryOid)) {
                    //关联交付清单
                    String targetOid = null;
                    String rootNodeName = "其他交付";
                    String categoryNodeName = "其他文档";

                    Delivery deliveryRoot = createDeliveryRoot(containerOid,rootNodeName);
                    log.info("Delivery root:{}", JSONUtil.toJsonStr(deliveryRoot));
                    targetOid = createCategoryNode(containerOid, categoryNodeName, deliveryRoot.getOid());

                    if (targetOid != null) {
                        deliveryOid = targetOid;
                        log.info("containerOid信息:{},deliveryOid信息: {}", containerOid, deliveryOid);
                    } else {
                        Assert.notNull(targetOid, "当前产品交付清单中未创建 其他交付-其他文档");
                    }
                }
                //2.1 在指定容器的 指定文件夹内创建数据
                com.alibaba.fastjson.JSONObject extensionContent = new com.alibaba.fastjson.JSONObject();
                extensionContent.put("cn_jwis_secrecy", "内部");
                extensionContent.put("source", "银河自研");
                extensionContent.put("deliveryOid", deliveryOid);
                doc.setExtensionContent(extensionContent);
                doc.setName(getFileNameWithoutExtension(fileName));

                LocationInfo locationInfo = new LocationInfo();
                locationInfo.setCatalogType(Folder.TYPE);
                locationInfo.setContainerType(Container.TYPE);
                locationInfo.setCatalogOid(folderOid);
                locationInfo.setContainerOid(containerOid);

                //doc的首次创建
                doc = creatDocForSoftWare(doc, locationInfo);
            }
            log.info("当前doc------>>>>>>{}", JSONUtil.toJsonStr(doc));

            String docFileTmpSavePath = tmpPath + fileName;
            List<File> primaryFile = new ArrayList<>();
            //提取钉钉流程中的文件
            primaryFile = downloadFileAndSetDocFile(downloadUri, docFileTmpSavePath, fileName);
            log.info("primaryFile------>>>>>>{}", JSONUtil.toJsonStr(primaryFile));


            //更新创建/变更文档下入库时间、创建时间

            //初次创建走检出检入 升小版本 ，变更升大版本
            if (CollectionUtil.isEmpty(doc.getPrimaryFile())) {
                // 首次上传文件，升小版本
                // 检出
                ModelInfo modelInfo = new ModelInfo();
                modelInfo.setOid(doc.getOid());
                modelInfo.setType(doc.getType());
                doc = (DocumentIteration) docAppService.checkOut(modelInfo);
                // 更新
                doc.setPrimaryFile(primaryFile);
                doc.setSecondaryFile(primaryFile);
                com.alibaba.fastjson.JSONObject extensionContent = doc.getExtensionContent();
                setExtensionCongtent(extensionContent, processInstanceId, processBusinessId, storageTime, subTime, doc);
                commonAbilityHelper.doUpdate(doc);
                log.info("first upload primaryFile doc:{}", JSONUtil.toJsonStr(doc));
                // 检入
                doc = (DocumentIteration) docAppService.checkIn(BeanUtil.copyProperties(doc, new LockInfo()));
            } else {
                // 非首次上传文件，升大版本
                if ("Released".equals(doc.getLifecycleStatus())) {
                    ModelInfo modelInfo = new ModelInfo();
                    modelInfo.setOid(doc.getOid());
                    modelInfo.setType(doc.getType());
                    doc = (DocumentIteration) docAppService.revise(modelInfo);
                }
                // 更新
                doc.setPrimaryFile(primaryFile);
                doc.setSecondaryFile(primaryFile);
                doc.setName(getFileNameWithoutExtension(fileName));
                com.alibaba.fastjson.JSONObject extensionContent = doc.getExtensionContent();
                setExtensionCongtent(extensionContent, processInstanceId, processBusinessId, storageTime, subTime, doc);
                log.info("change primaryFile doc:{}", JSONUtil.toJsonStr(doc));
                commonAbilityHelper.doUpdate(doc);
            }
            //更新doc状态为已发布
            customerCommonRepo.updateLifeStatus(DocumentIteration.TYPE, Arrays.asList(doc.getOid()), "Released");

            documentList.add(doc.getOid());

            //3、增加钉钉记录
            DingTaskRecord taskRecord = new DingTaskRecord();
            taskRecord.setDocumentIterationOidList(documentList);
            taskRecord.setDingProcessInstanceId(processInstanceId);
            taskRecord.setOid(OidGenerator.newOid());
            taskRecord.setOwner(SessionHelper.getCurrentUser().getAccount());
            taskRecord.setDingProcessName(isChange ? "软件配置项变更流程" : "软件配置项首次建库配置流程");
            commonService.create(taskRecord);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new JWIException("软件配置项首次建库配置流程有误： " + e.getMessage());
        }

        log.info("createOrChangeDocForSoftWareConfig END------>");
    }


    private String createCategoryNode(String containerOid, String otherWD, String oid) {
        DeliveryCreateDTO deliveryCreateDTO = new DeliveryCreateDTO();
        LocationInfo locationInfo1 = new LocationInfo();
        locationInfo1.setCatalogType(Container.TYPE);
        locationInfo1.setCatalogOid(containerOid);
        locationInfo1.setContainerModelDefinition("ProductContainer");
        locationInfo1.setContainerType(Container.TYPE);
        locationInfo1.setContainerOid(containerOid);
        deliveryCreateDTO.setLocationInfo(locationInfo1);

        com.alibaba.fastjson.JSONObject extensionContent = new com.alibaba.fastjson.JSONObject();
        extensionContent.put("FZR", " ");
        extensionContent.put("cn_yh_sfhs", "否");
        deliveryCreateDTO.setExtensionContent(extensionContent);

        deliveryCreateDTO.setModelDefinition("Category");
        deliveryCreateDTO.setName(otherWD);
        deliveryCreateDTO.setRoot(Boolean.FALSE);
        deliveryCreateDTO.setParentOid(oid);
        Delivery delivery = deliveryHelper.create(deliveryCreateDTO);
        log.info("Delivery Category:{}", JSONUtil.toJsonStr(delivery));
        return delivery.getOid();
    }

    private Delivery createDeliveryRoot(String containerOid, String name) {
        DeliveryCreateDTO deliveryCreateDTO = new DeliveryCreateDTO();
        LocationInfo locationInfo1 = new LocationInfo();
        locationInfo1.setCatalogType(Container.TYPE);
        locationInfo1.setCatalogOid(containerOid);
        locationInfo1.setContainerModelDefinition("ProductContainer");
        locationInfo1.setContainerType(Container.TYPE);
        locationInfo1.setContainerOid(containerOid);
        deliveryCreateDTO.setLocationInfo(locationInfo1);
        deliveryCreateDTO.setModelDefinition("Structure");
        deliveryCreateDTO.setName(name);
        deliveryCreateDTO.setRoot(Boolean.TRUE);
        Delivery delivery = deliveryHelper.create(deliveryCreateDTO);
        return delivery;
    }

    private static void setExtensionCongtent(com.alibaba.fastjson.JSONObject extensionContent, String processInstanceId, String processBusinessId, String storageTime, String subTime, DocumentIteration doc) {
        extensionContent.put("cn_jwis_secrecy", "内部");
        extensionContent.put("source", "银河自研");
        extensionContent.put("processInstanceId", processInstanceId);
        extensionContent.put("processBusinessId", processBusinessId);
        extensionContent.put("storageTime", storageTime);
        extensionContent.put("subTime", subTime);
        doc.setExtensionContent(extensionContent);
    }


    /**
     * 同意或拒绝审批流程
     *
     * @param record
     * @param assignee pdm审批人账号
     * @param taskName 当前工作里节点
     */
    public void finishApproval(DingTaskRecord record, String assignee, String taskName) {
        //当前审批人 钉钉的审批 ，需要确定是当前审批人的节点 同意，并非所有都同意
        String dingProcessInstanceId = record.getDingProcessInstanceId();

    }

    /**
     * 新版服务端API-获取单个审批实例详情接口
     *
     * @throws Exception
     */
    public GetProcessInstanceResponseBody processInstancesInfo(final String processInstanceId, final String accessToken) throws Exception {
        GetProcessInstanceResponseBody body = new GetProcessInstanceResponseBody();
        Client client = createClient();
        GetProcessInstanceHeaders headers = new GetProcessInstanceHeaders();
        headers.xAcsDingtalkAccessToken = accessToken;
        GetProcessInstanceRequest request = new GetProcessInstanceRequest()
                .setProcessInstanceId(processInstanceId);

        try {
            GetProcessInstanceResponse response = client.getProcessInstanceWithOptions(request, headers, new RuntimeOptions());
            body = response.getBody();
            log.info("Process instance body: {}", JSONUtil.toJsonStr(body));
        } catch (TeaException e) {
            handleTeaException(e);
        } catch (Exception e) {
            handleTeaException(new TeaException(e.getMessage(), e));
        }
        return body;
    }


    private void handleTeaException(TeaException e) {
        if (!Common.empty(e.code) && !Common.empty(e.message)) {
            log.error("TeaException occurred: code={}, message={}", e.code, e.message);
        }
    }


    @Override
    public void afterPropertiesSet() throws Exception {
    }

    public static Client createClient() throws Exception {
        Config config = new Config();
        config.protocol = "https";
        config.regionId = "central";
        return new Client(config);
    }

    public static com.aliyun.dingtalkstorage_1_0.Client createStorageClient() throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config();
        config.protocol = "https";
        config.regionId = "central";
        return new com.aliyun.dingtalkstorage_1_0.Client(config);
    }


    /**
     * 获取token
     *
     * @return
     * @throws Exception
     */
    public String getTokenNew() throws Exception {
        log.info("getTokenNew dingTalkHepler:{} , dingTalkAppSecretNew:{}", dingTalkAppKey, dingTalkAppSecret);
        Config config = new Config();
        config.protocol = "https";
        config.regionId = "central";
        com.aliyun.dingtalkoauth2_1_0.Client client = new com.aliyun.dingtalkoauth2_1_0.Client(config);
        GetAccessTokenRequest getAccessTokenRequest = new GetAccessTokenRequest()
                .setAppKey(dingTalkAppKey).setAppSecret(dingTalkAppSecret);
        GetAccessTokenResponse response = client.getAccessToken(getAccessTokenRequest);
        return response.getBody().getAccessToken();
    }

    /**
     * 下载审批附件
     *
     * @param processInstanceId
     * @param fileId
     * @param token
     * @return
     */
    public String getDINGFileDownloadUrl(String processInstanceId, String fileId, String token) {
        String downloadUri = "";
        try {
            Config config = new Config()
                    .setProtocol("https")
                    .setRegionId("central");
            Client client = new Client(config);
            GrantProcessInstanceForDownloadFileHeaders grantProcessInstanceForDownloadFileHeaders = new GrantProcessInstanceForDownloadFileHeaders();
            grantProcessInstanceForDownloadFileHeaders.xAcsDingtalkAccessToken = token;
            GrantProcessInstanceForDownloadFileRequest grantProcessInstanceForDownloadFileRequest = new GrantProcessInstanceForDownloadFileRequest()
                    .setProcessInstanceId(processInstanceId)
                    .setFileId(fileId);

            GrantProcessInstanceForDownloadFileResponse grantProcessInstanceForDownloadFileResponse = client.grantProcessInstanceForDownloadFileWithOptions(grantProcessInstanceForDownloadFileRequest, grantProcessInstanceForDownloadFileHeaders, new RuntimeOptions());
            GrantProcessInstanceForDownloadFileResponseBody body = grantProcessInstanceForDownloadFileResponse.getBody();
            if (body.getSuccess()) {
                downloadUri = body.getResult().getDownloadUri();
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return downloadUri;
    }


    /**
     * 授权下载审批钉盘文件
     *
     * @param fileId      文件ID
     * @param spaceId     空间ID
     * @param userId      用户ID
     * @param accessToken 钉钉 AccessToken
     * @throws Exception  授权失败时抛出异常
     */
    public static boolean authorizeDingTalkFileDownload(String fileId, Long spaceId, String userId, String accessToken) throws Exception {
        boolean result = false;

        // 初始化钉钉客户端
        Config config = new Config()
                .setProtocol("https")
                .setRegionId("central");
        Client client = new Client(config);

        // 配置请求头，设置 accessToken
        AddApproveDentryAuthHeaders headers = new AddApproveDentryAuthHeaders();
        headers.xAcsDingtalkAccessToken = accessToken;

        // 配置文件信息
        AddApproveDentryAuthRequest.AddApproveDentryAuthRequestFileInfos fileInfo = new AddApproveDentryAuthRequest.AddApproveDentryAuthRequestFileInfos()
                .setFileId(fileId)
                .setSpaceId(spaceId);

        // 构建请求
        AddApproveDentryAuthRequest request = new AddApproveDentryAuthRequest()
                .setUserId(userId)
                .setFileInfos(java.util.Collections.singletonList(fileInfo));

        // 执行授权操作
        try {
            AddApproveDentryAuthResponse response = client.addApproveDentryAuthWithOptions(request, headers, new RuntimeOptions());
            log.info("response: {}", JSONUtil.toJsonStr(response));
            AddApproveDentryAuthResponseBody body = response.getBody();

            if (body.getSuccess() != null && body.getSuccess() && body.getResult() != null && body.getResult()) {
                result = Boolean.TRUE;
                log.info("授权成功: fileId=" + fileId + ", spaceId=" + spaceId);
            } else {
                log.error("授权失败: 未返回预期结果");
                throw new RuntimeException("授权失败: 未返回预期结果");
            }
        } catch (TeaException e) {
            if (!com.aliyun.teautil.Common.empty(e.code) && !com.aliyun.teautil.Common.empty(e.message)) {
                log.error("授权失败，错误代码：" + e.code + "，错误信息：" + e.message);
                throw e;
            }
        } catch (Exception e) {
            TeaException teaException = new TeaException(e.getMessage(), e);
            if (!com.aliyun.teautil.Common.empty(teaException.code) && !com.aliyun.teautil.Common.empty(teaException.message)) {
                log.error("授权失败，错误代码：" + teaException.code + "，错误信息：" + teaException.message);
            }
            throw teaException;
        }
        return result;
    }


    /**
     * 获取文件下载信息
     *
     * @param spaceId    钉盘空间 ID
     * @param fileId     文件 ID
     * @param accessToken 钉钉 API Token
     * @return 下载信息中的 resourceUrl
     * @throws Exception
     */
    public static String getFileDownloadUrl(String spaceId, String fileId,String UnionId, String accessToken) throws Exception {
        // 初始化钉盘客户端
        com.aliyun.dingtalkstorage_1_0.Client storageClient = createStorageClient();
        // 配置请求头并设置 Token
        com.aliyun.dingtalkstorage_1_0.models.GetFileDownloadInfoHeaders headers = new com.aliyun.dingtalkstorage_1_0.models.GetFileDownloadInfoHeaders();
        headers.xAcsDingtalkAccessToken = accessToken;

        com.aliyun.dingtalkstorage_1_0.models.GetFileDownloadInfoRequest.GetFileDownloadInfoRequestOption option = new com.aliyun.dingtalkstorage_1_0.models.GetFileDownloadInfoRequest.GetFileDownloadInfoRequestOption()
                .setVersion(1L)
                .setPreferIntranet(false);
        com.aliyun.dingtalkstorage_1_0.models.GetFileDownloadInfoRequest request = new com.aliyun.dingtalkstorage_1_0.models.GetFileDownloadInfoRequest()
                .setUnionId(UnionId)
                .setOption(option);

        try {
            // 调用 API 获取文件下载信息
            GetFileDownloadInfoResponse fileDownloadInfoWithOptions = storageClient.getFileDownloadInfoWithOptions(spaceId, fileId, request, headers, new RuntimeOptions());
            GetFileDownloadInfoResponseBody.GetFileDownloadInfoResponseBodyHeaderSignatureInfo headerSignatureInfo = fileDownloadInfoWithOptions.getBody().getHeaderSignatureInfo();
            log.info("headerSignatureInfo信息:{}", JSONUtil.toJsonStr(headerSignatureInfo));
            Map<String, String> headers1 = headerSignatureInfo.getHeaders();
            List<String> resourceUrls = headerSignatureInfo.getResourceUrls();
            if (resourceUrls != null && resourceUrls.size() > 0) {
                return resourceUrls.get(0);
            } else {
                throw new RuntimeException("未获取到有效的 resourceUrl");
            }
        } catch (TeaException e) {
            if (!Common.empty(e.code) && !Common.empty(e.message)) {
                throw new RuntimeException("获取文件下载信息失败，错误代码：" + e.code + "，错误信息：" + e.message, e);
            }
            throw e;
        } catch (Exception e) {
            throw new RuntimeException("获取文件下载信息时发生未知错误", e);
        }

    }

    public static FileDownloadInfo getFileDownloadInfo(String spaceId, String fileId, String unionId, String accessToken) throws Exception {
        // 初始化钉盘客户端
        com.aliyun.dingtalkstorage_1_0.Client storageClient = createStorageClient();

        // 配置请求头并设置 Token
        com.aliyun.dingtalkstorage_1_0.models.GetFileDownloadInfoHeaders headers = new com.aliyun.dingtalkstorage_1_0.models.GetFileDownloadInfoHeaders();
        headers.xAcsDingtalkAccessToken = accessToken;

        // 配置请求选项
        com.aliyun.dingtalkstorage_1_0.models.GetFileDownloadInfoRequest.GetFileDownloadInfoRequestOption option = new com.aliyun.dingtalkstorage_1_0.models.GetFileDownloadInfoRequest.GetFileDownloadInfoRequestOption()
                .setVersion(1L)
                .setPreferIntranet(false);

        // 构建请求
        com.aliyun.dingtalkstorage_1_0.models.GetFileDownloadInfoRequest request = new com.aliyun.dingtalkstorage_1_0.models.GetFileDownloadInfoRequest()
                .setUnionId(unionId)
                .setOption(option);

        try {
            // 调用 API 获取文件下载信息
            GetFileDownloadInfoResponse response = storageClient.getFileDownloadInfoWithOptions(spaceId, fileId, request, headers, new RuntimeOptions());
            GetFileDownloadInfoResponseBody.GetFileDownloadInfoResponseBodyHeaderSignatureInfo signatureInfo = response.getBody().getHeaderSignatureInfo();

            // 提取资源 URL 和头信息
            Map<String, String> responseHeaders = signatureInfo.getHeaders();
            List<String> resourceUrls = signatureInfo.getResourceUrls();

            if (resourceUrls != null && !resourceUrls.isEmpty()) {
                return new FileDownloadInfo(resourceUrls.get(0), responseHeaders);
            } else {
                throw new RuntimeException("未获取到有效的 resourceUrl");
            }
        } catch (TeaException e) {
            if (!Common.empty(e.code) && !Common.empty(e.message)) {
                throw new RuntimeException("获取文件下载信息失败，错误代码：" + e.code + "，错误信息：" + e.message, e);
            }
            throw e;
        } catch (Exception e) {
            throw new RuntimeException("获取文件下载信息时发生未知错误", e);
        }
    }

  /*  private void initContext(String director) {
        UserDTO byAccount = this.userHelper.findByAccount(director);
        List<Tenant> tenants = this.tenantService.searchByUserOid(byAccount.getOid());
        if (CollectionUtil.isNotEmpty(tenants))
            byAccount.setTenantOid(tenants.get(0).getOid());
        SessionHelper.addCurrentUser(byAccount);
        SessionHelper.setAppId("pdm");
    }*/

    private void initContext(String account) {
        UserDTO byAccount = this.userHelper.findByAccount(account);
        if (byAccount == null) {
            throw new JWIException("该用户账号" + account + "不存在");
        }
        String tenantOid = commonService1.getAloneTenantOid();
        byAccount.setTenantOid(tenantOid);
        byAccount.setOid(byAccount.getOid());
        byAccount.setAccount(account);
        this.authHelper.fillUserAuthInfo(byAccount);
        String accessToken = SessionHelper.getAccessToken();
        SessionHelper.setAccessToken(accessToken);
        SessionHelper.addCurrentUser(byAccount);
        log.info("当前用户信息byAccount:{}", JSONUtil.toJsonStr(byAccount));
    }

    //
    public String getUserDetail(String userId,String token){
        try {
            Thread.sleep(50L);
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/get");
            OapiV2UserGetRequest req = new OapiV2UserGetRequest();
            req.setUserid(userId);
            String accessToken = token;
            OapiV2UserGetResponse rsp = client.execute(req, accessToken);
            log.info(JSON.toJSONString(rsp));

            if(rsp.getErrcode() != 0){
                return "";
            }
            return rsp.getBody();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public DocumentIteration creatDocForSoftWare(DocumentIteration doc, LocationInfo locationInfo) {

        log.info("DocumentIteration==>" + JSONUtil.toJsonStr(doc));

        ValidationUtil.validate(doc);
        // 确定 所在目录
        String catalogOid = locationInfo.getCatalogOid();
        String catalogType = locationInfo.getCatalogType();
        // 校验目录下重名，获取目录下的名称锁
        String name = doc.getName();
//        DocumentIteration doc = new DocumentIteration();
        doc.setModelDefinition("JWIGeneralDocument");
        RLock nameLock = redissonClient.getLock(MessageFormat.format(REDISSON_DOCUMENT_NAME_KEY, catalogOid, name));
        try {
            nameLock.tryLock();
            Assert.isTrue(nameLock.isHeldByCurrentThread(), "同名文档创建中:" + name);
            List<DocumentIteration> byName = findByNameAndCatalog(catalogType, catalogOid, name);
            Assert.isEmpty(byName, "目录下存在相同名称文档:" + name);
            documentService.setLocation(doc, locationInfo);
            // 设置分类
            Classification classification = classificationHelper.findByCode("cn_yh_rjpz");
            ClassificationInfo classificationInfo = BeanUtil.copyProperties(classification, new ClassificationInfo());
            documentService.setClassification(doc, classificationInfo);
            doc = commonAbilityHelper.doCreate(doc);
            //如果文档关联了分类则去找分类关联的交付清单类型节点。并将他与之挂关系CONTAIN
            /*if (classificationInfo != null) {
                Delivery result = deliveryHelper.findByClsOid(doc.getContainerOid(), classificationInfo.getOid());
                if (result != null) {
                    String deliveryOid = result.getOid();
                    String deliveryType = result.getType();
                    //挂关系Reference
                    List<RelationAble> rels = new ArrayList<>();
                    Reference reference = new Reference();
                    reference.setFromOid(deliveryOid);
                    reference.setFromType(deliveryType);
                    reference.setToOid(doc.getMasterOid());
                    reference.setToType(doc.getMasterType());
                    rels.add(reference);
                    commonAbilityService.createOutRelation(rels);
                }
            }*/

            if (StringUtils.isNotBlank(getDeliveryOidFromJson(doc.getExtensionContent()))) {
                bindToDelivery(doc.getMasterOid(), getDeliveryOidFromJson(doc.getExtensionContent()));
            }
        } finally {
            if (nameLock.isHeldByCurrentThread()) {
                nameLock.unlock();
            }
        }
        return doc;
    }

    private void bindToDelivery(String masterOid, String deliveryOid) {
        commonAbilityService.createOutRelation(createRelation(masterOid,deliveryOid));
    }
    private List<Reference> createRelation(String masterOid, String deliveryOid) {
        BaseEntity result = jwiCommonService.findByOid(Delivery.TYPE, deliveryOid,Delivery.class);
        if (ObjectUtils.isNotEmpty(result)) {
            Reference reference = new Reference();
            reference.setFromOid(result.getOid());
            reference.setFromType(result.getType());
            reference.setToOid(masterOid);
            reference.setToType(Document.TYPE);
            return Lists.newArrayList(reference);
        } else {
            return Lists.newArrayList();
        }
    }

    private String getDeliveryOidFromJson(com.alibaba.fastjson.JSONObject jsonObject) {
        if(jsonObject == null) {
            return StringUtils.EMPTY;
        } else {
            return jsonObject.getString("deliveryOid");
        }
    }

    public List<DocumentIteration> findByNameAndCatalog(String catalogType, String catalogOid, String name) {
        SubFilter subFilter = new SubFilter();
        subFilter.setFromType(catalogType);
        subFilter.setFromOid(catalogOid);
        subFilter.setType(Contain.TYPE);
        subFilter.setToType(Document.TYPE);
        subFilter.setFilter(Condition.where("name").eq(name));
        return documentService.findMIByFrom(subFilter);
    }

    private ArrayList<File> downloadFileAndSetDocFile(String downloadUri, String docFileTmpSavePath, String fileName) throws IOException {
        ArrayList<File> objects = new ArrayList<>();
        File file = new File();
        objects.add(file);
        //将文件下载到临时文件
        HttpUtil.downloadFile(downloadUri, FileUtil.file(docFileTmpSavePath));
        FileItem item = new DiskFileItemFactory().createItem("file", MediaType.MULTIPART_FORM_DATA_VALUE, true, fileName);
        InputStream pdfInputStream = new FileInputStream(docFileTmpSavePath);
        IOUtils.copy(pdfInputStream, item.getOutputStream());
        MultiResource multiResource = new MultiResource(Stream.of(new CommonsMultipartFile(item))
                .map(applyNoThrow(multipartFile -> new InputStreamResource(multipartFile.getInputStream(), multipartFile.getOriginalFilename()))).collect(Collectors.toList()));
        String fileUploadUrl = fileServiceGatewayUrl + "/file/upload", accesstoken;
        accesstoken = (accesstoken = SessionHelper.getAccessToken()) != null ? accesstoken : "eyJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJKV0lTIiwiaWF0IjoxNjk4ODQwOTI4LCJzdWIiOiJzeXNfYWRtaW4iLCJleHAiOjE2OTk0NDU3MjgsInVzZXIiOnsib2lkIjoic3lzX2FkbWluIiwidGVuYW50T2lkIjoiIiwidGVuYW50TmFtZSI6bnVsbCwiYWNjb3VudCI6InN5c19hZG1pbiIsIm5hbWUiOiLns7vnu5_nrqHnkIblkZgiLCJwYXNzd29yZCI6bnVsbCwicGhvbmUiOm51bGwsImVtYWlsIjpudWxsLCJnZW5kZXIiOjAsImRlc2NyaXB0aW9uIjpudWxsLCJhdmF0YXIiOm51bGwsImRpc2FibGUiOjAsInNlY3VyaXR5TGV2ZWwiOjk5OSwiaWNvbiI6bnVsbCwib2xkUGFzc3dvcmQiOm51bGwsImNvbmZpcm1QYXNzd29yZCI6bnVsbCwibmV3UGFzc3dvcmQiOm51bGwsInRpcHMiOm51bGwsImludmFsaWRUaW1lIjpudWxsLCJjcmVhdGVCeSI6bnVsbCwic2lnbmF0dXJlIjpudWxsLCJjcmVhdGVEYXRlIjowLCJ1cGRhdGVCeSI6bnVsbCwidXBkYXRlRGF0ZSI6MH0sImFjY291bnQiOiJzeXNfYWRtaW4iLCJ1c2VyT2lkIjoic3lzX2FkbWluIn0.Mk5efWzWg03MUpHkw1YAHVJEcqC2RqDWIjC43U0ZSnI";
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("accesstoken", accesstoken);
        String respStr = HttpRequest.post(fileUploadUrl).timeout(30000).addHeaders(headerMap).form("file", multiResource).execute().body();
        log.info("upload resp==>" + respStr);
        JSONObject respJson = new JSONObject(respStr);
        FileMetadata result1 = JSONUtil.toBean(respJson.getJSONObject("result").getJSONObject(fileName), FileMetadata.class);
        file.setName(result1.getFileOriginalName());
        file.setOid(result1.getOid());
        return objects;
    }

    //处理流程中的文件名称
    public static String getFileNameWithoutExtension(String fileName) {
        // 空值检查
        if (fileName == null || fileName.isEmpty()) {
            return "空文件名称"; // 返回空字符串或抛出异常，取决于业务需求
        }

        // 查找最后一个 '.' 的索引
        int dotIndex = fileName.lastIndexOf('.');

        // 如果没有 '.' 或 '.' 在文件名的开头，则说明没有后缀，直接返回文件名
        if (dotIndex == -1 || dotIndex == 0) {
            return fileName;
        }

        // 返回文件名不包含后缀的部分
        return fileName.substring(0, dotIndex);
    }

    public static String extractContainerOid(String extValue) {
        if (extValue == null || extValue.isEmpty()) {
            return null;
        }
        try {
            JSONObject jsonObject = JSONUtil.parseObj(extValue);
            return jsonObject.getStr("key");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }


    /**
     * 文件外发流程 审批同意后发送 接收人邮箱
     * @param bizData 钉钉流程数据
     */
    public void dealFileOutSend(com.alibaba.fastjson.JSONObject bizData) {
        log.info("dealFileOutSend START------>");
        String processInstanceId = bizData.getString("processInstanceId");
        String processBusinessId = bizData.getString("businessId");//审批单号
        try {
            //1、获取钉钉审批流程数据
            String token = getTokenNew();
            GetProcessInstanceResponseBody getProcessInstanceResponseBody = processInstancesInfo(processInstanceId, token);
            GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResult result = getProcessInstanceResponseBody.getResult();
            log.info("文件外发流程钉钉表单result------>:{}", JSONUtil.toJsonStr(result));
            List<GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultFormComponentValues> formComponentValues = result.getFormComponentValues();
            List<GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultOperationRecords> operationRecords = result.getOperationRecords();

            String title = result.getTitle();//xxx提交的xxx流程
            String originatorUserId = result.getOriginatorUserId();
            String unionId = dingTalkService.getUserUnionid(originatorUserId, token);

            String dynamicPart = StrUtil.subBefore(title, "提交的", false) + "提交的";
            String finalSubject = dynamicPart + "文件外发" + processBusinessId;

            String outSendFileList = null;
            String outSendDetail = null;
            // 获取附件下载路径
            List<java.io.File> outSendFile = new ArrayList<>();

            //提取外发回执清单附件
            for (GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultOperationRecords record : operationRecords) {
                log.info("operation_records信息:{}", JSONUtil.toJsonStr(record));

                if ("外发回执清单附件:".equals(record.getRemark()) && "ADD_REMARK".equals(record.getType())) {
                    List<GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultOperationRecordsAttachments> attachments = record.getAttachments();

                    for (GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultOperationRecordsAttachments attachment : attachments) {
                        String fileName = attachment.getFileName();
                        String spaceId = attachment.getSpaceId();
                        String fileId = attachment.getFileId();
                        boolean isSuccess = authorizeDingTalkFileDownload(fileId, Long.valueOf(spaceId), originatorUserId, token);
                        if (!isSuccess) {
                            log.info("授权失败: attachment:{} ", JSONUtil.toJsonStr(attachment));
                            continue;
                        }
//                        getFileDownloadUrl(spaceId, fileId, unionId, token);
                        FileDownloadInfo downloadInfo = getFileDownloadInfo(spaceId, fileId, unionId, token);
                        System.out.println("文件下载 URL: " + downloadInfo.getResourceUrl());
                        System.out.println("文件头信息: " + downloadInfo.getHeaders());
//                        String downloadUri = getDINGFileDownloadUrl(processInstanceId, fileId,token);

                        // 构建唯一的文件路径
                        String docFileTmpSavePath = buildFilePath(tmpPath, fileSeparator, "attachment", fileName);

                        java.io.File file = FileUtil.file(docFileTmpSavePath);
                        try {
                            log.info("正在下载文件: {} 到路径: {}", fileName, docFileTmpSavePath);
                            // 使用 Hutool 的 HttpRequest 下载文件，添加自定义 headers
                            HttpRequest request = HttpRequest.get(downloadInfo.getResourceUrl());
                            downloadInfo.getHeaders().forEach(request::header); // 添加 headers

                            // 执行请求并保存文件
                            request.execute().writeBody(file);
//                            HttpUtil.downloadFile(downloadUri, file);
                            outSendFile.add(file);
                            log.info("文件下载成功: {}", fileName);
                        } catch (Exception e) {
                            log.error("文件下载失败: {}，错误信息: {}", fileName, e.getMessage(), e);
                        }
                    }
                    // 如果只需要处理第一个匹配的记录，则保留 break
                    break;
                }
            }
            //初始化账号信息
            List<String> emailList = new ArrayList<>();
            //提取文件接收人、外发文件相关信息
            for (GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultFormComponentValues componentValue : formComponentValues) {
                String name = componentValue.getName();
                // 如果 name 为空，则直接跳过
                if (StrUtil.isEmpty(name)) {
                    continue;
                }
                switch (name) {
                    case "文件接收人":
                        log.info("文件接收人: {}", JSONUtil.toJsonStr(componentValue));
                        String extValue = componentValue.getExtValue();
                        // 解析 extValue 并提取 emplId
                        setEmailInfo(extValue, token, emailList);
                        break;
                    case "外发文件":
                        outSendFileList = componentValue.getValue();
                        break;
                    case "明细":
                        outSendDetail = componentValue.getValue();
                        break;
                    default:
                        break;
                }
            }

            String emailContent = generateTableFromJson(outSendDetail);
            JSONArray jsonArray = JSONUtil.parseArray(outSendFileList);

            if (jsonArray != null && !jsonArray.isEmpty()) {
                log.info("文件外发jsonArray{}", JSONUtil.toJsonStr(jsonArray));
                // 获取第一个元素并转换为JSONObject
                jsonArray.forEach(obj -> {
                    JSONObject jsonObject = (JSONObject) obj;
                    String fileId = jsonObject.getStr("fileId");
                    String fileName = jsonObject.getStr("fileName");
                    String downloadUri = getDINGFileDownloadUrl(processInstanceId, fileId, token);

                    String docFileTmpSavePath = buildFilePath(tmpPath, fileSeparator, "form", fileName);

                    // 提取钉钉流程中的文件
                    java.io.File file = FileUtil.file(docFileTmpSavePath);
                    try {
                        log.info("正在下载文件: {} 到路径: {}", fileName, docFileTmpSavePath);
                        HttpUtil.downloadFile(downloadUri, file);
                        outSendFile.add(file);
                        log.info("文件下载完成: {}", fileName);
                    } catch (Exception e) {
                        log.error("文件下载失败: {}，错误信息: {}", fileName, e.getMessage(), e);
                        throw new JWIException("文件外发下载失败: " + fileName);
                    }
                });
                // 调用 FileZipUtil 进行 ZIP 打包
                String zipFilePath = tmpPath + "fileOutSend" + fileSeparator +
                        DateUtil.format(new Date(), "yyyyMMddHHmmss") + fileSeparator +
                        finalSubject + ".zip";
                java.io.File zipFile = this.createZipFile(outSendFile, zipFilePath);

                // 将 ZIP 文件作为附件发送邮件
                List<java.io.File> zipFileList = new ArrayList<>();
                zipFileList.add(zipFile);
                fIleOutMailUtil.sendMailToMultipleRecipients("PDM文件外发",emailList, finalSubject, emailContent, zipFileList);
                log.info("ZIP 文件已成功发送至邮件: {}", zipFilePath);
            } else {
                throw new JWIException("文件外发-信息化平台钉钉流程 外发附件为空 ");
            }

//            log.info("外发文件outSendFile------>>>>>>{}", JSONUtil.toJsonStr(outSendFile));
//            fIleOutMailUtil.sendMailToMultipleRecipients(emailList, finalSubject, emailContent,outSendFile);

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new JWIException("文件外发-信息化平台流程有误： " + e.getMessage());
        }

        log.info("dealFileOutSend END------>");

    }

    private void setEmailInfo(String extValue, String accessToken, List<String> emailList) {
        if (StrUtil.isBlank(extValue)) {
            return; // 如果 extValue 为空，直接返回
        }

        List<JSONObject> extValueList = JSONUtil.toList(JSONUtil.parseArray(extValue), JSONObject.class);
        for (JSONObject jsonObject : extValueList) {
            String emplId = jsonObject.getStr("emplId");
            if (StrUtil.isBlank(emplId)) {
                continue; // 如果 emplId 为空，跳过当前循环
            }

            // 通过钉钉 userID 查询对应的邮箱
            String userDetail = getUserDetail(emplId, accessToken);
            if (StrUtil.isBlank(userDetail)) {
                continue; // 如果 userDetail 为空，跳过当前循环
            }

            // 解析用户详情 JSON
            com.alibaba.fastjson.JSONObject userDetailJson = JSON.parseObject(userDetail);
            com.alibaba.fastjson.JSONObject result = userDetailJson.getJSONObject("result");
            if (result == null) {
                continue; // 如果 result 为空，跳过当前循环
            }

            String email = result.getString("email");
            if (StrUtil.isNotBlank(email)) {
                emailList.add(email); // 将邮箱加入列表
            }
        }
    }

    public static String generateTableFromJson(String outSendDetail) {
        // 解析 JSON 数组
        JSONArray jsonArray = new JSONArray(outSendDetail);

        // 用于存储表格的 HTML 代码
        StringBuilder tableHtml = new StringBuilder();
        tableHtml.append("<table border=\"1\" cellpadding=\"5\" cellspacing=\"0\" style=\"border-collapse:collapse;\">");

        // 检查 jsonArray 是否为空
        if (jsonArray.isEmpty()) {
            return "<p>没有明细数据</p>";
        }

        // 获取第一个元素（假设是包含表格数据的数组）
        JSONObject rowData = jsonArray.getJSONObject(0);

        // 提取 rowValue，这里是一个数组
        JSONArray rowValues = rowData.getJSONArray("rowValue");

        // 创建表头
        tableHtml.append("<thead><tr>");
        // 添加序号列
        tableHtml.append("<th>序号</th>");

        // 添加其他列标题
        for (int i = 0; i < rowValues.size(); i++) {
            JSONObject column = rowValues.getJSONObject(i);
            String label = column.getStr("label");
            tableHtml.append("<th>").append(label).append("</th>");
        }
        tableHtml.append("</tr></thead>");

        // 创建表格主体
        tableHtml.append("<tbody>");
        for (int i = 0; i < jsonArray.size(); i++) {
            // 获取每一行数据
            rowData = jsonArray.getJSONObject(i);
            rowValues = rowData.getJSONArray("rowValue");

            // 每行的序号列
            tableHtml.append("<tr>");
            tableHtml.append("<td>").append(i + 1).append("</td>"); // 序号从1开始

            // 添加每行的其他列数据
            for (int j = 0; j < rowValues.size(); j++) {
                JSONObject column = rowValues.getJSONObject(j);
                String value = column.getStr("value");
                tableHtml.append("<td>").append(value).append("</td>");
            }
            tableHtml.append("</tr>");
        }
        tableHtml.append("</tbody>");

        // 关闭表格标签
        tableHtml.append("</table>");

        return tableHtml.toString();
    }

    /**
     * 将多个文件打包为 ZIP 文件
     *
     * @param files       待打包的文件列表
     * @param zipFilePath ZIP 文件生成路径
     * @return 生成的 ZIP 文件
     */
    public static java.io.File createZipFile(List<java.io.File> files, String zipFilePath) {
        if (files == null || files.isEmpty()) {
            throw new IllegalArgumentException("待打包的文件列表不能为空");
        }

        try {
            // 使用 Hutool 打包文件
            java.io.File zipFile = FileUtil.file(zipFilePath);
            cn.hutool.core.util.ZipUtil.zip(zipFile, false, files.toArray(new java.io.File[0]));
            return zipFile;
        } catch (Exception e) {
            throw new RuntimeException("创建 ZIP 文件失败", e);
        }
    }
    /**
     * 构建文件的保存路径
     *
     * @param basePath      基础路径
     * @param fileSeparator 文件分隔符
     * @param subFolder     子文件夹名称
     * @param fileName      文件名
     * @return 构建的完整路径
     */
    private String buildFilePath(String basePath, String fileSeparator, String subFolder, String fileName) {
        String timeStamp = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
        // 使用 UUID 防止文件冲突
        String uniqueId = UUID.randomUUID().toString();
        return basePath + "fileOutSend" + fileSeparator + subFolder + fileSeparator + timeStamp + fileSeparator + uniqueId + fileSeparator + fileName;
    }

    /**
     * 处理钉钉物料申请流程中的特定字段提取和关联
     * 提取是否辐照敏感、元器件详细标准号、有效贮存期（年）字段到part的extensionContent
     * 处理抗辐照指标附件，创建document并建立REFERENCE_KFZ关联
     *
     * @param processInstanceId 钉钉流程实例ID
     * @param dingTaskRecord 钉钉任务记录
     */
    public void processPartExtensionAndRadiationDocuments(String processInstanceId, DingTaskRecord dingTaskRecord) {
        log.info("开始处理物料申请流程中的扩展字段和抗辐照指标文档 processInstanceId: {}", processInstanceId);

        try {
            String token = getTokenNew();
            List<GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultFormComponentValues> formComponentValues =
                    DingTalkServiceImpl.getDingFromValues(processInstanceId, token);

            for (GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultFormComponentValues formComponentValue : formComponentValues) {
                String name = formComponentValue.getName();
                if (StrUtil.isNotEmpty(name) && "申请物料明细".equals(name)) {
                    // 物料明细Json
                    String wlmxJson = formComponentValue.getValue();
                    log.info("物料明细wlmxJson: {}", wlmxJson);
                    cn.hutool.json.JSONArray jsonArray = JSONUtil.parseArray(wlmxJson);

                    for (int i = 0; i < jsonArray.size(); i++) {
                        cn.hutool.json.JSONObject obj = jsonArray.getJSONObject(i);
                        cn.hutool.json.JSONArray rowValueArray = obj.getJSONArray("rowValue");

                        // 提取当前行的part oid
                        String oidValue = getStringByKeyName(rowValueArray, "oid", "");
                        if (StrUtil.isEmpty(oidValue)) {
                            log.warn("当前行未找到part oid，跳过处理");
                            continue;
                        }

                        PartIteration part = partHelper.findByOid(oidValue);
                        if (part == null) {
                            log.warn("未找到oid为{}的Part对象，跳过处理", oidValue);
                            continue;
                        }

                        // 提取扩展字段
                        String isRadiationSensitive = getStringByKeyName(rowValueArray, "是否辐照敏感", "");
                        String componentDetailStandard = getStringByKeyName(rowValueArray, "元器件详细标准号", "");
                        String effectiveStorageYears = getStringByKeyName(rowValueArray, "有效贮存期（年）", "");

                        // 更新part的extensionContent
                        updatePartExtensionContent(part, isRadiationSensitive, componentDetailStandard, effectiveStorageYears, dingTaskRecord);

                        // 处理抗辐照指标附件
                        String radiationResistanceFiles = getStringByKeyName(rowValueArray, "抗辐照指标", "[]");
                        processRadiationResistanceDocuments(radiationResistanceFiles, part, processInstanceId, dingTaskRecord);
                    }
                }
            }

            log.info("物料申请流程中的扩展字段和抗辐照指标文档处理完成");

        } catch (Exception e) {
            log.error("处理物料申请流程中的扩展字段和抗辐照指标文档时发生错误: {}", e.getMessage(), e);
            throw new JWIException("处理物料申请流程中的扩展字段和抗辐照指标文档失败: " + e.getMessage());
        }
    }

    /**
     * 更新Part的extensionContent属性
     *
     * @param part Part对象
     * @param CN_YH_RAD_SENSITIVE 是否辐照敏感
     * @param CN_YH_PART_STD_NO 元器件详细标准号
     * @param CN_YH_SHELF_LIFE 有效贮存期（年）
     * @param dingTaskRecord 钉钉任务记录
     */
    private void updatePartExtensionContent(PartIteration part, String CN_YH_RAD_SENSITIVE,
                                          String CN_YH_PART_STD_NO, String CN_YH_SHELF_LIFE,
                                          DingTaskRecord dingTaskRecord) {
        try {
            log.info("开始更新Part {} 的extensionContent", part.getOid());

            // 获取或创建extensionContent
            com.alibaba.fastjson.JSONObject extensionContent = part.getExtensionContent();
            if (extensionContent == null) {
                extensionContent = new com.alibaba.fastjson.JSONObject();
            }

            // 更新字段
            if (StrUtil.isNotEmpty(CN_YH_RAD_SENSITIVE)) {
                extensionContent.put("CN_YH_RAD_SENSITIVE", CN_YH_RAD_SENSITIVE);
                log.info("设置是否辐照敏感: {}", CN_YH_RAD_SENSITIVE);
            }

            if (StrUtil.isNotEmpty(CN_YH_PART_STD_NO)) {
                extensionContent.put("CN_YH_PART_STD_NO", CN_YH_PART_STD_NO);
                log.info("设置元器件详细标准号: {}", CN_YH_PART_STD_NO);
            }

            if (StrUtil.isNotEmpty(CN_YH_SHELF_LIFE)) {
                extensionContent.put("CN_YH_SHELF_LIFE", CN_YH_SHELF_LIFE);
                log.info("设置有效贮存期（年）: {}", CN_YH_SHELF_LIFE);
            }

            // 设置流程相关信息

            part.setExtensionContent(extensionContent);
            commonAbilityHelper.doUpdate(part);

            log.info("Part {} extensionContent更新完成", part.getOid());

        } catch (Exception e) {
            log.error("更新Part extensionContent时发生错误: {}", e.getMessage(), e);
            throw new JWIException("更新Part extensionContent失败: " + e.getMessage());
        }
    }

    /**
     * 处理抗辐照指标文档
     *
     * @param radiationResistanceFiles 抗辐照指标附件JSON字符串
     * @param part Part对象
     * @param processInstanceId 钉钉流程实例ID
     * @param dingTaskRecord 钉钉任务记录
     */
    private void processRadiationResistanceDocuments(String radiationResistanceFiles, PartIteration part,
                                                   String processInstanceId, DingTaskRecord dingTaskRecord) {
        if (StrUtil.isEmpty(radiationResistanceFiles) || "[]".equals(radiationResistanceFiles)) {
            log.info("Part {} 没有抗辐照指标附件，跳过处理", part.getOid());
            return;
        }

        try {
            log.info("开始处理Part {} 的抗辐照指标文档", part.getOid());
            cn.hutool.json.JSONArray fileJsonArray = JSONUtil.parseArray(radiationResistanceFiles);

            if (fileJsonArray == null || fileJsonArray.size() == 0) {
                log.info("抗辐照指标附件数组为空，跳过处理");
                return;
            }

            String token = getTokenNew();

            for (int i = 0; i < fileJsonArray.size(); i++) {
                cn.hutool.json.JSONObject fileObject = fileJsonArray.getJSONObject(i);
                String fileId = fileObject.getStr("fileId");
                String fileName = fileObject.getStr("fileName");

                if (StrUtil.isEmpty(fileId) || StrUtil.isEmpty(fileName)) {
                    log.warn("文件ID或文件名为空，跳过处理: fileId={}, fileName={}", fileId, fileName);
                    continue;
                }

                log.info("处理抗辐照指标文件: {}", fileName);

                // 下载文件
                String downloadUri = getDINGFileDownloadUrl(processInstanceId, fileId, token);
                String docFileTmpSavePath = tmpPath + fileName;
                List<File> primaryFiles = downloadFileAndSetDocFile(downloadUri, docFileTmpSavePath, fileName);

                com.alibaba.fastjson.JSONObject extensionContent = part.getExtensionContent();
                extensionContent.put("CN_YH_RAD_SENSITIVE_FILE", primaryFiles);
                commonAbilityHelper.doUpdate(part);
                // 创建Document
                DocumentIteration document = createRadiationResistanceDocument(fileName, primaryFiles, part, dingTaskRecord);

                // 建立REFERENCE_KFZ关联
                createReferenceKfzRelation(part, document);

                log.info("抗辐照指标文档 {} 创建并关联完成", document.getOid());
            }

        } catch (Exception e) {
            log.error("处理抗辐照指标文档时发生错误: {}", e.getMessage(), e);
            throw new JWIException("处理抗辐照指标文档失败: " + e.getMessage());
        }
    }

    /**
     * 创建抗辐照指标文档
     *
     * @param fileName 文件名
     * @param primaryFiles 主文件列表
     * @param part 关联的Part对象
     * @param dingTaskRecord 钉钉任务记录
     * @return 创建的DocumentIteration对象
     */
    private DocumentIteration createRadiationResistanceDocument(String fileName, List<File> primaryFiles,
                                                              PartIteration part, DingTaskRecord dingTaskRecord) {
        try {
            log.info("开始创建抗辐照指标文档: {}", fileName);

            DocumentIteration document = new DocumentIteration();
            document.setName(getFileNameWithoutExtension(fileName));
            document.setModelDefinition("JWIGeneralDocument");
            // 设置位置信息，与Part在同一位置
            LocationInfo locationInfo = new LocationInfo();
            locationInfo.setCatalogOid(part.getCatalogOid());
            locationInfo.setContainerOid(part.getContainerOid());
            locationInfo.setCatalogType(part.getCatalogType());
            locationInfo.setContainerType(part.getContainerType());
            locationInfo.setContainerModelDefinition(part.getContainerModelDefinition());

            // 获取或创建deliveryOid
            String deliveryOid = null;
            String containerOid = part.getContainerOid();
            if (StrUtil.isEmpty(deliveryOid)) {
                // 关联交付清单
                String rootNodeName = "其他交付";
                String categoryNodeName = "其他文档";

                Delivery deliveryRoot = createDeliveryRoot(containerOid, rootNodeName);
                log.info("Delivery root:{}", JSONUtil.toJsonStr(deliveryRoot));
                String targetOid = createCategoryNode(containerOid, categoryNodeName, deliveryRoot.getOid());

                if (targetOid != null) {
                    deliveryOid = targetOid;
                    log.info("containerOid信息:{},deliveryOid信息: {}", containerOid, deliveryOid);
                } else {
                    log.warn("当前产品交付清单中未创建 其他交付-其他文档，将不关联交付清单");
                }
            }

            // 设置扩展内容
            com.alibaba.fastjson.JSONObject extensionContent = new com.alibaba.fastjson.JSONObject();
            extensionContent.put("cn_jwis_secrecy", "内部");
            extensionContent.put("source", "银河自研");
            extensionContent.put("processInstanceId", dingTaskRecord.getDingProcessInstanceId());
            extensionContent.put("processBusinessId", dingTaskRecord.getBusinessId());
            extensionContent.put("storageTime", System.currentTimeMillis());
            extensionContent.put("subTime", System.currentTimeMillis());
            if (StrUtil.isNotEmpty(deliveryOid)) {
                extensionContent.put("deliveryOid", deliveryOid);
            }
            document.setExtensionContent(extensionContent);

            // 设置文件
            document.setPrimaryFile(primaryFiles);

            // 设置文档位置
            documentService.setLocation(document, locationInfo);

            // 设置分类（如果需要）
            Classification classification = classificationHelper.findByCode("cn_jwis_sjlwj"); // 抗辐照指标分类
            if (classification != null) {
                ClassificationInfo classificationInfo = BeanUtil.copyProperties(classification, new ClassificationInfo());
                documentService.setClassification(document, classificationInfo);
            }

            // 创建文档
            document = commonAbilityHelper.doCreate(document);
            log.info("文档创建成功，oid: {}", document.getOid());

            // 绑定到交付清单
            if (StrUtil.isNotEmpty(deliveryOid)) {
                bindToDelivery(document.getMasterOid(), deliveryOid);
                log.info("文档已绑定到交付清单: {}", deliveryOid);
            }

            // 设置状态为已发布
            customerCommonRepo.updateLifeStatus(DocumentIteration.TYPE, Arrays.asList(document.getOid()), "Released");

            log.info("抗辐照指标文档创建完成: {}", document.getOid());
            return document;

        } catch (Exception e) {
            log.error("创建抗辐照指标文档时发生错误: {}", e.getMessage(), e);
            throw new JWIException("创建抗辐照指标文档失败: " + e.getMessage());
        }
    }

    /**
     * 创建Part到Document的REFERENCE_KFZ关联关系
     *
     * @param part Part对象
     * @param document Document对象
     */
    private void createReferenceKfzRelation(PartIteration part, DocumentIteration document) {
        try {
            log.info("开始创建Part {} 到Document {} 的REFERENCE_KFZ关联", part.getOid(), document.getOid());

            RelatedBatchOperate operate = new RelatedBatchOperate();
            ArrayList<String> slaveOids = new ArrayList<>();
            slaveOids.add(document.getOid());

            operate.setMainObjectType(part.getModelDefinition());
            operate.setMainObjectOid(part.getOid());
            operate.setRelationshipName("REFERENCE_KFZ");
            operate.setRelationConstraint(RelationConstraint.MM);
            operate.setForward(Boolean.TRUE); // setForward设置为true
            operate.setSlaveObjectType(document.getModelDefinition());
            operate.setSlaveObjectOids(slaveOids);
            operate.setMainObjectClassName(part.getType());
            operate.setSlaveObjectClassName(document.getType());

            instanceHelper.batchAddRelated(operate);
            log.info("REFERENCE_KFZ关联关系创建成功");

        } catch (Exception e) {
            log.error("创建REFERENCE_KFZ关联关系时发生错误: {}", e.getMessage(), e);
            throw new JWIException("创建REFERENCE_KFZ关联关系失败: " + e.getMessage());
        }
    }

    /**
     * 从钉钉表单行数据中根据label获取对应的value值
     *
     * @param rowValueArray 行数据数组
     * @param keyName 要查找的label名称
     * @param defaultValue 默认值
     * @return 找到的value值或默认值
     */
    private static String getStringByKeyName(cn.hutool.json.JSONArray rowValueArray, String keyName, String defaultValue) {
        for (int k = 0; k < rowValueArray.size(); k++) {
            cn.hutool.json.JSONObject innerRowObj = rowValueArray.getJSONObject(k);
            if (keyName.equals(innerRowObj.getStr("label"))) {
                String value = innerRowObj.getStr("value");
                return StrUtil.isNotEmpty(value) ? value : defaultValue;
            }
        }
        return defaultValue;
    }

    /**
     * 获取不带扩展名的文件名
     *
     * @param fileName 完整文件名
     * @return 不带扩展名的文件名
     */
/*    private String getFileNameWithoutExtension(String fileName) {
        if (StrUtil.isEmpty(fileName)) {
            return fileName;
        }
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0) {
            return fileName.substring(0, lastDotIndex);
        }
        return fileName;
    }*/

}
