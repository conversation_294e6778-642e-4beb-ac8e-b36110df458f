package cn.jwis.product.pdm.customer.service.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * PDM用户认证信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PdmUserAuthInfo {
    
    @ApiModelProperty("访问令牌")
    private String accessToken;
    
    @ApiModelProperty("刷新令牌")
    private String refreshToken;
    
    @ApiModelProperty("令牌类型")
    private String tokenType;
    
    @ApiModelProperty("令牌过期时间(秒)")
    private Long expiresIn;
    
    @ApiModelProperty("令牌作用域")
    private String scope;
    
    @ApiModelProperty("用户信息")
    private PdmUserInfo userInfo;
}
