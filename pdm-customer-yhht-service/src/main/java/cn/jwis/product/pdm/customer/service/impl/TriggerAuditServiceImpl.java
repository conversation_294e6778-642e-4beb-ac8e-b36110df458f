package cn.jwis.product.pdm.customer.service.impl;

import cn.jwis.audit.annotation.JWIParam;
import cn.jwis.audit.annotation.JWIServiceAudit;
import cn.jwis.audit.enums.Action;
import cn.jwis.framework.base.domain.able.info.ModelInfo;
import cn.jwis.framework.base.domain.entity.BaseEntity;
import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.platform.plm.account.user.service.UserHelper;
import cn.jwis.platform.plm.datadistribution.annotation.DataDistribution;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.foundation.common.param.VersionInfo;
import cn.jwis.platform.plm.foundation.location.able.info.LocationInfo;
import cn.jwis.platform.plm.foundation.versionrule.able.info.LockInfo;
import cn.jwis.product.pdm.baseline.entity.Baseline;
import cn.jwis.product.pdm.baseline.entity.BaselineItemDTO;
import cn.jwis.product.pdm.change.entity.ECR;
import cn.jwis.product.pdm.change.entity.Issue;
import cn.jwis.product.pdm.change.response.ChangeInfo;
import cn.jwis.product.pdm.delivery.dto.MasterDTO;
import cn.jwis.product.pdm.delivery.entity.Delivery;
import cn.jwis.product.pdm.document.dto.LifecycleStatusUpdateDTO;
import cn.jwis.product.pdm.document.entity.DocumentIteration;
import cn.jwis.product.pdm.partbom.part.entity.PartIteration;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR> yefei
 * @Date ： 2024/3/13
 * @Description :
 */
@Component
public class TriggerAuditServiceImpl {

    @Resource
    JWICommonService commonService;

    @Resource
    UserHelper userHelper;

    @JWIServiceAudit(
            buzType = "DocumentIteration",
            action = Action.IMPORT,
            content = "文档导入,导入文件名称:${result}"
    )
    @JWIParam("result")
    @DataDistribution(
            dataOpt = "import"
    )
    public String importDoc(String fileName) {
        return fileName;
    }

    @JWIServiceAudit(
            buzOid = "${ecr.oid}",
            bizNumber = "${ecr.number}",
            bizName = "${ecr.name}",
            buzType = "${ecr.type}",
            action = Action.UPDATE,
            content = "增加变更对象到变更单,变更单名称:${ecr.name}; 添加对象名称:${result.name},类型:${result.type},编码:${result.number}," +
                    "标识:${result.oid}"
    )
    @JWIParam("result")
    public ChangeInfo addChangeInfo(ECR ecr, ModelInfo modelInfo) {
        return queryChangeInfo(modelInfo);
    }

    @JWIServiceAudit(
            buzOid = "${ecr.oid}",
            bizNumber = "${ecr.number}",
            bizName = "${ecr.name}",
            buzType = "${ecr.type}",
            action = Action.UPDATE,
            content = "从变更单删除变更对象,变更单名称:${ecr.name};删除对象名称名称:${result.name},类型:${result.type},编码:${result.number}," +
                    "标识:${result.oid}"
    )
    @JWIParam("result")
    public ChangeInfo deleteChangeInfo(ECR ecr,ModelInfo modelInfo) {
        return queryChangeInfo(modelInfo);
    }

    private ChangeInfo queryChangeInfo(ModelInfo modelInfo) {
        return commonService.findByOid(modelInfo.getType(),modelInfo.getOid(),ChangeInfo.class);
    }

    @JWIServiceAudit(
            buzOid = "${entity.oid}",
            bizNumber = "${result.number}",
            bizName = "${result.name}",
            buzType = "${entity.type}",
            action = Action.MOVE,
            content = "移动对象类型:${entity.type},标识:${entity.oid} 到目标下;目标类型:${target.catalogType},目标标识:${target.catalogOid}"
    )
    @JWIParam("result")
    public BaseEntity move(ModelInfo entity, LocationInfo target) {
       BaseEntity catalog = (BaseEntity) commonService.findEntity(entity.getType(),entity.getOid());
       return catalog;
    }

    @JWIServiceAudit(
            buzOid = "${baseEntity.oid}",
            buzType = "${baseEntity.type}",
            bizNumber = "${baseEntity.number}",
            bizName = "${baseEntity.name}",
            action = Action.UPDATE,
            content = "设置对象owner,设置owner为姓名:${result.name},账号:${result.account}"
    )
    @JWIParam("result")
    public UserDTO setOwnerAudit(BaseEntity baseEntity, String ownerAccount) {
        UserDTO userDTO = userHelper.findByAccount(ownerAccount);
        return userDTO;
    }

    @JWIServiceAudit(
            buzOid = "${delivery.oid}",
            buzType = "${delivery.type}",
            bizNumber = "${delivery.number}",
            bizName = "${delivery.name}",
            action = Action.UPDATE,
            content = "关联对象类型:${dto.masterType},标识:${dto.masterOid};到交付清单 名称:${result.name}"
    )
    @JWIParam("result")
    public Delivery batchAddInstance(MasterDTO dto, Delivery delivery) {
        return delivery;
    }

    @JWIServiceAudit(
            buzOid = "${result.oid}",
            buzType = "${result.type}",
            bizNumber = "${result.number}",
            bizName = "${result.name}",
            action = Action.UPDATE,
            content = "删除关联对象类型:${dto.masterType},标识:${dto.masterOid};从交付清单 名称:${result.name}"
    )
    @JWIParam("result")
    public Delivery batchDeleteInstance(MasterDTO dto, Delivery delivery) {
        return delivery;
    }

    @JWIServiceAudit(
            buzOid = "${result.oid}",
            buzType = "${result.type}",
            bizNumber = "${result.number}",
            bizName = "${result.name}",
            action = Action.DELETE,
            content = "删除交付清单条目,名称:${result.name}"
    )
    @JWIParam("result")
    public Delivery deleteDelivery(Delivery delivery) {
        return delivery;
    }

    @JWIServiceAudit(
            buzOid = "${result.oid}",
            buzType = "${result.type}",
            bizNumber = "${result.number}",
            bizName = "${result.name}",
            action = Action.DELETE,
            content = "删除基线,名称:${result.name}"
    )
    @JWIParam("result")
    public Baseline deleteBaseLine(String oid) {
        return commonService.findByOid(Baseline.TYPE,oid,Baseline.class);
    }

    @JWIServiceAudit(
            buzOid = "${result.oid}",
            buzType = "${result.type}",
            bizNumber = "${result.number}",
            bizName = "${result.name}",
            action = Action.UPDATE,
            content = "添加对象至基线,对象名称:${dto.name},编码:${dto.number},类型:${dto.wideType},标识:${dto.oid};基线名称${result.name}"
    )
    @JWIParam("result")
    public Baseline batchLink(BaselineItemDTO dto,String baseLineOid) {
        return commonService.findByOid(Baseline.TYPE,baseLineOid,Baseline.class);
    }

    @JWIServiceAudit(
            buzOid = "${result.oid}",
            buzType = "${result.type}",
            bizNumber = "${result.number}",
            bizName = "${result.name}",
            action = Action.UPDATE,
            content = "从基线删除对象,标识:${oid};基线名称${result.name}"
    )
    @JWIParam("result")
    public Baseline batchDeleteLink(String oid,String baseLineOid) {
        return commonService.findByOid(Baseline.TYPE,baseLineOid,Baseline.class);
    }

    @JWIServiceAudit(
            buzOid = "${processInsId}",
            buzType = "ProcessInstance",
            action = Action.START,
            content = "发起钉钉流程,流程名称:${name}"
    )
    @JWIParam("result")
    public void startDingProcess(String name,String processInsId) {
    }

    @JWIServiceAudit(buzOid = "${documentIteration.oid}",
            bizNumber = "${documentIteration.number}",
            buzType = "${documentIteration.type}",
            bizName = "${documentIteration.name}",
            action = Action.RENAME,
            content = "重命名文档,更改后的名称为：${name}")
    @JWIParam("result")
    public void docRename(DocumentIteration documentIteration,String name) {
    }

    @JWIServiceAudit(buzOid = "${partIteration.oid}",
            bizNumber = "${partIteration.number}",
            buzType = "${partIteration.type}",
            bizName = "${partIteration.name}",
            action = Action.RENAME,
            content = "重命名部件,更改后的名称为：${name}")
    @JWIParam("result")
    public void partRename(PartIteration partIteration, String name) {
    }

    @JWIServiceAudit(
            buzOid = "${result.oid}",
            buzType = "${result.type}",
            bizNumber = "${result.number}",
            bizName = "${result.name}",
            action = Action.UPDATE,content = "设置问题单状态,问题单名称:${result.name},设置状态为:${dto.status}")
    @JWIParam("result")
    public Issue setIssueLife(LifecycleStatusUpdateDTO dto) {
        return commonService.findByOid(Issue.TYPE,dto.getModelInfo().getOid(),Issue.class);
    }

    @JWIServiceAudit(
            buzOid = "${result.oid}",
            bizNumber = "${result.number}",
            buzType = "${result.type}",
            bizName = "${result.name}",
            action = Action.DELETE,
            content = "删除文档名称为:${result.name}")
    @JWIParam("result")
    @DataDistribution(dataOpt = "delete")
    public DocumentIteration deleteDoc(DocumentIteration documentIteration) {
        return documentIteration;
    }

    @JWIServiceAudit(
            buzOid = "${result.oid}",
            bizNumber = "${result.number}",
            buzType = "${result.type}",
            bizName = "${result.name}",
            action = Action.DELETE,
            content = "删除Part名称为:${result.name}")
    @JWIParam("result")
    @DataDistribution(dataOpt = "delete")
    public PartIteration deletePart(PartIteration partIteration) {
        return partIteration;
    }

    @JWIServiceAudit(
            buzOid = "${result.oid}",
            bizNumber = "${result.number}",
            buzType = "${result.type}",
            bizName = "${result.name}",
            action = Action.CHENOUT,
            content = "检出")
    @JWIParam("result")
    public BaseEntity checkOut(ModelInfo nodeInfo) {
        return (BaseEntity) commonService.findEntity(nodeInfo.getType(), nodeInfo.getOid());
    }

    @JWIServiceAudit(
            buzOid = "${result.oid}",
            bizNumber = "${result.number}",
            buzType = "${result.type}",
            bizName = "${result.name}",
            action = Action.CHECKIN,
            content = "检入")
    @JWIParam("result")
    public BaseEntity checkIn(LockInfo nodeInfo) {
        return (BaseEntity) commonService.findEntity(nodeInfo.getType(), nodeInfo.getOid());
    }

    @JWIServiceAudit(
            buzOid = "${result.oid}",
            bizNumber = "${result.number}",
            buzType = "${result.type}",
            bizName = "${result.name}",
            action = Action.UNCHECKOUT,
            content = "取消检出")
    @JWIParam("result")
    public BaseEntity cancelCheckOut(ModelInfo nodeInfo) {
        return (BaseEntity) commonService.findEntity(nodeInfo.getType(), nodeInfo.getOid());
    }

    @JWIServiceAudit(
            buzOid = "${result.oid}",
            bizNumber = "${result.number}",
            buzType = "${result.type}",
            bizName = "${result.name}",
            action = Action.UPDATE,
            content = "设置对象 类型:${result.type},名称:${result.name} 状态为:${status}")
    @JWIParam("result")
    public BaseEntity setStatus(ModelInfo nodeInfo,String status) {
        return (BaseEntity) commonService.findEntity(nodeInfo.getType(), nodeInfo.getOid());
    }

    @JWIServiceAudit(
            buzOid = "${result.oid}",
            bizNumber = "${result.number}",
            buzType = "${result.type}",
            bizName = "${result.name}",
            action = Action.UPDATE,
            content = "修订 对象类型:${result.type},名称:${result.name}")
    @JWIParam("result")
    public BaseEntity revise(VersionInfo nodeInfo) {
        return (BaseEntity) commonService.findEntity(nodeInfo.getType(), nodeInfo.getOid());
    }

    @JWIServiceAudit(
            buzOid = "${result.oid}",
            bizNumber = "${result.number}",
            buzType = "${result.type}",
            bizName = "${result.name}",
            action = Action.UPDATE,
            content = "修订 类型:${result.type},名称:${result.name}"
    )
    @JWIParam("result")
    public BaseEntity revise(ModelInfo nodeInfo) {
        return (BaseEntity)this.commonService.findEntity(nodeInfo.getType(), nodeInfo.getOid());
    }

}
